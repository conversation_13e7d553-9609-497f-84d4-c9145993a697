# -*- coding: utf-8 -*-
from odoo import http
from odoo.http import request
import json
import logging
from odoo.exceptions import UserError
from datetime import datetime

_logger = logging.getLogger(__name__)


class EquipmentInspectionDashboard(http.Controller):
    """设备巡检数据看板控制器"""

    @http.route('/equipment_inspection/dashboard', type='http', auth='user', methods=['GET'])
    def equipment_inspection_dashboard(self):
        """设备巡检数据仪表板HTML页面"""
        try:
            # 使用Odoo的方式读取静态文件
            from odoo.tools import file_open
            
            # 尝试读取HTML文件
            with file_open('ylhc_metro_mobile/static/src/views/equipment_inspection_dashboard.html', 'r') as f:
                html_content = f.read()
            
            return request.make_response(
                html_content,
                headers=[('Content-Type', 'text/html; charset=utf-8')]
            )
        except Exception as e:
            _logger.error("设备巡检看板页面加载失败: %s", str(e))
            return request.make_response(
                '<h1>页面加载失败</h1><p>错误: ' + str(e) + '</p>',
                headers=[('Content-Type', 'text/html; charset=utf-8')]
            )

    @http.route('/api/equipment_inspection/dashboard_data', type='json', auth='user', methods=['POST'])
    def get_dashboard_data(self):
        """获取设备巡检仪表板数据，所有统计都支持按月过滤、按线路过滤和按专业过滤"""
        try:
            equipment_inspection = request.env['equipment.inspection']
            params = request.httprequest.json or {}
            month = params.get('month')
            line_id = params.get('line_id')
            major_id = params.get('major_id')  # 专业过滤
            _logger.info(f"获取设备巡检仪表板数据请求，月份参数: {month}, 线路参数: {line_id}, 专业参数: {major_id}")

            # 调用模型方法获取数据
            try:
                data = equipment_inspection.get_dashboard_data(month=month, line_id=line_id, major_id=major_id)
                _logger.info(f"模型方法调用成功，返回数据类型: {type(data)}")
                _logger.info(f"返回设备巡检仪表板数据: {data}")
            except Exception as model_error:
                _logger.error(f"模型方法调用失败: {str(model_error)}")
                import traceback
                _logger.error(f"详细错误信息: {traceback.format_exc()}")
                raise model_error
            
            # 确保返回数据格式正确
            result = {
                'success': True,
                'total_count': data.get('total_count', 0),
                'completed_count': data.get('completed_count', 0),
                'abnormal_count': data.get('abnormal_count', 0),
                'has_note_count': data.get('has_note_count', 0),
                'major_stats': data.get('major_stats', []),
                'system_stats': data.get('system_stats', []),
                'station_stats': data.get('station_stats', []),
                'station_issue_stats': data.get('station_issue_stats', [])
            }
            
            return result
        except Exception as e:
            _logger.error(f"获取设备巡检仪表板数据失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'total_count': 0,
                'completed_count': 0,
                'abnormal_count': 0,
                'has_note_count': 0,
                'major_stats': [],
                'system_stats': [],
                'station_stats': [],
                'station_issue_stats': []
            }

    @http.route('/api/equipment_inspection/drill_down', type='json', auth='user', methods=['POST'])
    def drill_down_data(self):
        """获取下钻数据"""
        try:
            equipment_inspection = request.env['equipment.inspection']
            params = request.httprequest.json or {}
            
            drill_type = params.get('type')  # 'major', 'system', 'station', 'problem'
            drill_id = params.get('id')
            month = params.get('month')
            line_id = params.get('line_id')
            major_id = params.get('major_id')
            
            if drill_type == 'major' and drill_id:
                # 专业下钻到系统
                data = equipment_inspection.get_system_stats(
                    month=month, 
                    line_id=line_id,
                    major_id=drill_id
                )
                return {'success': True, 'result': data}
            elif drill_type == 'system' and drill_id:
                # 系统下钻 - 可以扩展为子系统统计
                # 这里暂时返回该系统的详细信息
                system = request.env['inspection.system'].browse(int(drill_id))
                data = [{
                    'name': line.name,
                    'value': 1,  # 可以根据实际需求统计
                    'id': line.id
                } for line in system.inspection_system_line_ids]
                return {'success': True, 'result': data}
            else:
                # 其他类型的下钻可以根据需要扩展
                return {'success': True, 'result': []}
                
        except Exception as e:
            _logger.error(f"获取下钻数据失败: {str(e)}")
            return {'success': False, 'error': str(e), 'result': []}

    @http.route('/api/equipment_inspection/lines', type='http', auth='user', methods=['GET'], csrf=False)
    def get_lines(self):
        """获取线路列表"""
        try:
            lines = request.env['metro.line'].search([])
            if not lines:
                _logger.warning("未找到任何线路数据")
                data = []
            else:
                data = [{'id': line.id, 'name': line.name} for line in lines]
                _logger.info(f"成功获取 {len(data)} 条线路数据")
            
            response = request.make_response(
                json.dumps(data, ensure_ascii=False),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )
            return response
        except Exception as e:
            _logger.error(f"获取线路列表失败: {str(e)}")
            return request.make_response(
                json.dumps({'error': str(e)}, ensure_ascii=False),
                headers=[('Content-Type', 'application/json; charset=utf-8')],
                status=500
            )

    @http.route('/api/equipment_inspection/majors', type='http', auth='user', methods=['GET'], csrf=False)
    def get_majors(self):
        """获取专业列表"""
        try:
            majors = request.env['inspection.major'].search([])
            if not majors:
                _logger.warning("未找到任何专业数据")
                data = []
            else:
                data = [{'id': major.id, 'name': major.name} for major in majors]
                _logger.info(f"成功获取 {len(data)} 条专业数据")
            
            response = request.make_response(
                json.dumps(data, ensure_ascii=False),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )
            return response
        except Exception as e:
            _logger.error(f"获取专业列表失败: {str(e)}")
            return request.make_response(
                json.dumps({'error': str(e)}, ensure_ascii=False),
                headers=[('Content-Type', 'application/json; charset=utf-8')],
                status=500
            )

    @http.route('/api/equipment_inspection/trends', type='json', auth='user', methods=['POST'])
    def get_trends_data(self):
        """获取巡检趋势数据"""
        try:
            equipment_inspection = request.env['equipment.inspection']
            params = request.httprequest.json or {}
            
            days = params.get('days', 30)
            line_id = params.get('line_id')
            major_id = params.get('major_id')
            view_mode = params.get('view_mode', 'daily')  # daily 或 weekly
            month = params.get('month')  # 月份参数
            
            _logger.info(f"获取设备巡检趋势数据请求，天数: {days}, 线路: {line_id}, 专业: {major_id}, 模式: {view_mode}, 月份: {month}")
            
            # 先检查是否有数据
            total_records = equipment_inspection.search_count([])
            _logger.info(f"数据库中总共有 {total_records} 条设备巡检记录")
            
            if total_records == 0:
                # 如果没有数据，返回模拟数据进行测试
                _logger.info("数据库中没有设备巡检记录，返回模拟数据")
                from datetime import datetime, timedelta
                
                if view_mode == 'weekly':
                    trends_data = {
                        'dates': ['第49周', '第50周', '第51周', '第52周'],
                        'total': [25, 32, 28, 31],
                        'completed': [22, 28, 24, 27],
                        'abnormal': [3, 4, 4, 4]
                    }
                else:
                    # 生成最近30天的模拟数据
                    dates = []
                    total = []
                    completed = []
                    abnormal = []
                    
                    for i in range(days):
                        date = datetime.now().date() - timedelta(days=days-1-i)
                        dates.append(date.strftime('%m-%d'))
                        
                        # 模拟数据：总数在1-3之间，完成率85%左右
                        import random
                        total_count = random.randint(1, 3)
                        completed_count = int(total_count * random.uniform(0.8, 0.95))
                        abnormal_count = random.randint(0, 1)
                        
                        total.append(total_count)
                        completed.append(completed_count)
                        abnormal.append(abnormal_count)
                    
                    trends_data = {
                        'dates': dates,
                        'total': total,
                        'completed': completed,
                        'abnormal': abnormal
                    }
                
                efficiency_data = {
                    'completion_rate': 87.5,
                    'abnormal_rate': 12.5,
                    'note_rate': 18.3,
                    'avg_duration': 65.8,
                    'total_count': total_records
                }
            else:
                # 有数据时调用实际方法
                trends_data = equipment_inspection.get_inspection_trends(
                    days=days, 
                    line_id=line_id,
                    major_id=major_id,
                    view_mode=view_mode,
                    month=month
                )
                
                # 获取效率统计数据
                efficiency_data = equipment_inspection.get_inspection_efficiency_stats(
                    line_id=line_id,
                    major_id=major_id,
                    month=month
                )
            
            result = {
                'success': True,
                'trends': trends_data,
                'efficiency': efficiency_data
            }
            
            _logger.info(f"返回设备巡检趋势数据成功，数据长度: dates={len(trends_data.get('dates', []))}")
            return result
            
        except Exception as e:
            _logger.error(f"获取设备巡检趋势数据失败: {str(e)}")
            import traceback
            _logger.error(f"错误详情: {traceback.format_exc()}")
            return {
                'success': False,
                'error': str(e),
                'trends': {'dates': [], 'total': [], 'completed': [], 'abnormal': []},
                'efficiency': {'completion_rate': 0, 'abnormal_rate': 0, 'note_rate': 0, 'avg_duration': 0}
            }
