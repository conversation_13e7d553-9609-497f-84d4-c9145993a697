from odoo import models, fields, api, _
from odoo.exceptions import UserError
from datetime import datetime, timedelta
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT



class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    def copy_purchase_line_info(self):
        self.ensure_one()
        
        lines_text = []
        for line in self.order_line:
            product_name = line.product_id.display_name or line.name or ''
            price_unit = round(line.price_total/line.product_uom_qty, 4)
            line_text = f"{product_name}, 单价: {price_unit:.4f}, 数量: {line.product_uom_qty}, 金额: {line.price_total:.4f}"
            lines_text.append(line_text)
        
        if not lines_text:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('警告'),
                    'message': _('当前订单没有产品行信息'),
                    'sticky': False,
                    'type': 'warning',
                }
            }
        
        copy_text = "\n".join(lines_text)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'ylch_copy_to_clipboard',
            'params': {
                'text': copy_text,
            }
        } 

    def action_update_order_line(self):
        """
        从价格表更新产品价格和预计到货时间
        """
        SupplierInfo = self.env['product.supplierinfo']
        updated_count = 0
        
        for line in self.order_line:
            if not line.product_id or not self.partner_id:
                continue
                
            # 查找该产品的所有供应商价格信息
            supplier_infos = SupplierInfo.search([
                ('partner_id', '=', self.partner_id.id),
                ('product_id', '=', line.product_id.id),
            ])
            
            # 获取对应的供应商信息记录，用于计算交货时间
            seller = line.product_id._select_seller(
                partner_id=self.partner_id,
                quantity=line.product_qty,
                date=self.date_order and self.date_order.date() or fields.Date.context_today(line),
                uom_id=line.product_uom,
            )
            
            if supplier_infos:
                # 根据价格阶梯逻辑选择合适的价格
                # 找到所有满足最低数量要求的记录
                applicable_infos = supplier_infos.filtered(
                    lambda x: x.min_qty <= line.product_qty
                )
                
                if applicable_infos:
                    # 选择min_qty最大的记录（最接近当前数量的阶梯）
                    best_match = applicable_infos.sorted(key=lambda x: x.min_qty, reverse=True)[0]
                    line.price_unit = best_match.price
                    updated_count += 1
                else:
                    # 如果没有满足最低数量的记录，选择min_qty最小的记录
                    fallback_match = supplier_infos.sorted(key=lambda x: x.min_qty)[0]
                    line.price_unit = fallback_match.price
                    updated_count += 1
            
            # 重新计算预计到货时间
            if seller or not line.date_planned:
                line.date_planned = line._get_date_planned(seller).strftime(DEFAULT_SERVER_DATETIME_FORMAT)
        
            
        if updated_count > 0:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('更新完成'),
                    'message': _('已更新 %s 行价格') % updated_count,
                    'type': 'success',
                    'sticky': False,
                    'next': {
                        'type': 'ir.actions.client',
                        'tag': 'soft_reload',
                    }
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('提示'),
                    'message': _('没有找到可更新的价格信息'),
                    'type': 'info',
                    'sticky': False,
                }
            }

    def action_generate_supplierinfo(self):
        """
        根据询价单行信息生成或更新供应商价格表
        """
        self.ensure_one()
        SupplierInfo = self.env['product.supplierinfo']

        if not self.partner_id:
            raise UserError(_('请先设置供应商！'))

        if not self.order_line:
            raise UserError(_('询价单中没有订单行！'))

        # 确定开始日期，使用订单日期或确认日期或当前日期
        start_date = self.date_approve or self.date_order.date() or fields.Date.today()

        created_count = updated_count = 0
        for line in self.order_line:
            if not line.product_id or line.product_id.type == 'service':
                continue

            # 查找是否已存在相同的供应商价格表
            domain = [
                ('partner_id', '=', self.partner_id.id),
                ('product_id', '=', line.product_id.id),
                ('min_qty', '=', line.product_qty),
                ('price', '=', line.price_unit),
            ]

            existing_info = SupplierInfo.search(domain, limit=1)

            # 准备供应商价格表的值
            supplier_values = {
                'partner_id': self.partner_id.id,
                'product_id': line.product_id.id,
                'product_name': line.product_id.name,
                'product_code': line.product_id.default_code or '',
                'min_qty': line.product_qty,
                'price': line.price_unit,
                'date_start': start_date,
                'x_purchase_order_id': self.id,
                'x_taxes_id': [(6, 0, line.taxes_id.ids)] if line.taxes_id else False,
            }

            if existing_info:
                # 更新现有价格表
                existing_info.write(supplier_values)
                updated_count += 1
            else:
                # 创建新价格表
                SupplierInfo.create(supplier_values)
                created_count += 1

        message = _('已成功创建 %s 条和更新 %s 条供应商价格表记录。') % (
            created_count, updated_count)
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('操作完成'),
                'message': message,
                'sticky': False,
                'type': 'success',
                'next': {'type': 'ir.actions.act_window_close'},
            }
        }


class PurchaseOrderLine(models.Model):
    _inherit = 'purchase.order.line'

    # 添加计算字段，用于控制同步按钮的显示
    show_price_sync_btn = fields.Boolean(
        string='Show Price Sync Button',
        compute='_compute_show_price_sync_btn',
        store=False,
    )

    supplier_pricelist_count = fields.Integer(
        string='Supplier Pricelist Count',
        compute='_compute_show_price_sync_btn',
        store=False,
    )

    @api.onchange('product_id')
    def copy_product_template_name(self):
        for line in self:
            tmpl_name = line.product_id.product_tmpl_id.name or ''
            if tmpl_name:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'ylch_copy_to_clipboard',
                    'params': {
                        'text': tmpl_name,
                    }
                }

    @api.onchange('product_id', 'order_id.partner_id', 'price_unit', 'product_qty')
    def _compute_show_price_sync_btn(self):
        """计算是否显示价格同步按钮"""
        for line in self:
            if not line.product_id or not line.order_id.partner_id or not line.price_unit:
                line.show_price_sync_btn = False
                line.supplier_pricelist_count = 0
                continue

            # 检查是否已存在供应商价格信息
            supplier_info = self.env['product.supplierinfo'].search([
                ('partner_id', '=', line.order_id.partner_id.id),
                ('product_id', '=', line.product_id.id),
            ])

            line.supplier_pricelist_count = len(supplier_info)

            if not supplier_info:
                # 没有价格表时可以新建价格，显示按钮为可操作状态
                line.show_price_sync_btn = True
            else:
                # 有价格表时，根据价格阶梯逻辑选择合适的价格进行比较
                # 找到所有满足最低数量要求的记录（min_qty <= 当前采购数量）
                applicable_info = supplier_info.filtered(
                    lambda x: x.min_qty <= line.product_qty
                )
                
                if applicable_info:
                    # 在满足条件的记录中，选择min_qty最大的（最接近当前数量的阶梯）
                    best_match = applicable_info.sorted(key=lambda x: x.min_qty, reverse=True)[0]
                    # 价格不同时可以操作，价格相同时不可操作
                    if abs(best_match.price - line.price_unit) > 0.01:
                        line.show_price_sync_btn = True
                    else:
                        # 价格相同时不可操作，但按钮仍显示（通过CSS样式控制为灰色）
                        line.show_price_sync_btn = False
                else:
                    # 没有满足最低数量要求的记录，说明当前采购数量小于所有现有阶梯
                    # 这种情况下可以创建新的价格阶梯
                    line.show_price_sync_btn = True

    def action_sync_to_pricelist(self):
        """将当前行同步到价格表"""

        # 查找是否已存在相同的价格表项目
        existing_item = self.env['product.pricelist.item'].search([
            ('pricelist_id', '=', self.order_id.pricelist_id.id),
            ('product_id', '=', self.product_id.id),
            ('min_quantity', '=', self.product_qty),
        ], limit=1)

        if existing_item:
            # 更新现有项目
            existing_item.write({
                'fixed_price': self.price_unit,
            })
            message = _('已更新价格表项目，价格：%s') % self.price_unit
        else:
            # 创建新项目
            self.env['product.pricelist.item'].create({
                'pricelist_id': self.order_id.pricelist_id.id,
                'product_id': self.product_id.id,
                'min_quantity': self.product_qty,
                'fixed_price': self.price_unit,
                'applied_on': '0_product_variant',
            })
            message = _('已创建新的价格表项目，价格：%s') % self.price_unit

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('同步完成'),
                'message': message,
                'sticky': False,
                'type': 'success',
            }
        }

    def action_sync_price_to_pricelist(self):
        """同步价格到供应商价格表的按钮动作 - 打开向导"""
        # 首先检查是否允许同步操作
        if hasattr(self, 'show_price_sync_btn') and not self.show_price_sync_btn:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('提示'),
                    'message': _('当前价格与供应商价格表中的价格相同，无需同步'),
                    'type': 'info',
                    'sticky': False,
                }
            }
        
        # 确保有采购订单行ID
        line_id = self.id
        if not line_id:
            # 如果是未保存的记录，需要先保存
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('提示'),
                    'message': _('请先保存采购订单后再进行价格同步'),
                    'type': 'warning',
                    'sticky': False,
                }
            }
        
        # 打开向导
        return {
            'type': 'ir.actions.act_window',
            'name': "同步到供应商价格表向导",
            'res_model': 'purchase.price.sync.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_purchase_line_id': line_id,
                'active_id': line_id,
                'active_model': 'purchase.order.line',
            }
        }

    def _sync_price_for_product(self, product):
        """为指定产品同步价格（用于未保存的记录）"""
        # 从上下文获取必要信息
        if not self.id:
            # 未保存的记录，从上下文获取信息
            order_id = self.env.context.get('order_id')
            price_unit = self.env.context.get('price_unit')
            product_qty = self.env.context.get('product_qty', 1.0)
            company_id = self.env.context.get(
                'company_id', self.env.company.id)

            if not order_id:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('错误'),
                        'message': _('缺少订单信息'),
                        'type': 'danger',
                        'sticky': False,
                    }
                }

            order = self.env['purchase.order'].browse(order_id)
            partner_id = order.partner_id.id

            if not price_unit:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('错误'),
                        'message': _('缺少价格信息'),
                        'type': 'danger',
                        'sticky': False,
                    }
                }
        else:
            # 已保存的记录，直接从self获取信息
            partner_id = self.order_id.partner_id.id
            price_unit = self.price_unit
            product_qty = self.product_qty
            company_id = self.company_id.id

        try:
            partner = self.env['res.partner'].browse(partner_id)

            # 先查找完全匹配的供应商价格信息（相同产品、供应商和数量）
            exact_match = self.env['product.supplierinfo'].search([
                ('partner_id', '=', partner.id),
                ('product_id', '=', product.id),
                ('min_qty', '=', product_qty),
            ], limit=1)

            if exact_match:
                # 更新现有的完全匹配记录
                old_price = exact_match.price
                exact_match.write({
                    'price': price_unit,
                    'date_start': fields.Date.today(),
                })
                message = _('已更新供应商价格信息：%s → %s (数量: %s)') % (old_price, price_unit, product_qty)
            else:
                # 查找相同产品和供应商但不同数量的记录
                similar_records = self.env['product.supplierinfo'].search([
                    ('partner_id', '=', partner.id),
                    ('product_id', '=', product.id),
                ])

                if similar_records:
                    # 如果存在相似记录，可以选择更新最接近的数量记录或创建新记录
                    # 这里我们选择创建新记录以保持价格阶梯的完整性
                    pass

                # 创建新的供应商价格信息
                supplier_values = {
                    'partner_id': partner.id,
                    'product_id': product.id,
                    'product_name': product.name,
                    'product_code': product.default_code or '',
                    'min_qty': product_qty,
                    'price': price_unit,
                    'date_start': fields.Date.today(),
                }

                # 如果是从订单创建，添加订单引用
                if hasattr(self, 'order_id') and self.order_id:
                    supplier_values['x_purchase_order_id'] = self.order_id.id
                elif order_id:
                    supplier_values['x_purchase_order_id'] = order_id

                self.env['product.supplierinfo'].create(supplier_values)
                if similar_records:
                    message = _('已为供应商创建新的价格阶梯，价格：%s (数量: %s)') % (price_unit, product_qty)
                else:
                    message = _('已创建供应商价格信息，价格：%s (数量: %s)') % (price_unit, product_qty)

            # 强制刷新缓存
            self.env.flush_all()
            self.env.invalidate_all()

            # 重新计算显示状态
            self._compute_show_price_sync_btn()

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('成功'),
                    'message': message,
                    'type': 'success',
                    'sticky': False,
                    'next': {
                        'type': 'ir.actions.client',
                        'tag': 'soft_reload',
                    }
                }
            }

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(
                f"Error syncing price for product {product.id}: {str(e)}", exc_info=True)

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('错误'),
                    'message': _('同步价格失败：%s') % str(e),
                    'type': 'danger',
                    'sticky': False,
                }
            }

    def _sync_price_for_existing_record(self):
        """为已保存的记录同步价格（原有逻辑）"""
        if not self.product_id or not self.price_unit:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('错误'),
                    'message': _('缺少产品或价格信息'),
                    'type': 'danger',
                    'sticky': False,
                }
            }

        partner = self.order_id.partner_id
        if not partner:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('错误'),
                    'message': _('订单缺少供应商信息'),
                    'type': 'danger',
                    'sticky': False,
                }
            }

        try:
            # 先查找完全匹配的供应商价格信息（相同产品、供应商和数量）
            exact_match = self.env['product.supplierinfo'].search([
                ('partner_id', '=', partner.id),
                ('product_id', '=', self.product_id.id),
                ('min_qty', '=', self.product_qty),
            ], limit=1)

            if exact_match:
                # 更新现有的完全匹配记录
                old_price = exact_match.price
                exact_match.write({
                    'price': self.price_unit,
                    'date_start': fields.Date.today(),
                })
                message = _('已更新供应商价格信息：%s → %s (数量: %s)') % (old_price, self.price_unit, self.product_qty)
            else:
                # 查找相同产品和供应商但不同数量的记录
                similar_records = self.env['product.supplierinfo'].search([
                    ('partner_id', '=', partner.id),
                    ('product_id', '=', self.product_id.id),
                ])

                # 创建新的供应商价格信息
                supplier_values = {
                    'partner_id': partner.id,
                    'product_id': self.product_id.id,
                    'product_name': self.product_id.name,
                    'product_code': self.product_id.default_code or '',
                    'min_qty': self.product_qty,
                    'price': self.price_unit,
                    'date_start': fields.Date.today(),
                    'x_purchase_order_id': self.order_id.id,
                }

                self.env['product.supplierinfo'].create(supplier_values)
                if similar_records:
                    message = _('已为供应商创建新的价格阶梯，价格：%s (数量: %s)') % (self.price_unit, self.product_qty)
                else:
                    message = _('已创建供应商价格信息，价格：%s (数量: %s)') % (self.price_unit, self.product_qty)

            # 强制刷新缓存和重新计算
            self.env.flush_all()
            self.env.invalidate_all()

            # 重新计算显示状态
            self._compute_show_price_sync_btn()

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('成功'),
                    'message': message,
                    'type': 'success',
                    'sticky': False,
                    'next': {
                        'type': 'ir.actions.client',
                        'tag': 'soft_reload',
                    }
                }
            }

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(
                f"Error syncing price to supplier info: {str(e)}", exc_info=True)

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('错误'),
                    'message': _('同步价格失败：%s') % str(e),
                    'type': 'danger',
                    'sticky': False,
                }
            }
