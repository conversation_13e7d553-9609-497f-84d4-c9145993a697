/* =================== 禁用Element Plus（饿了么UI）动画和过渡效果 =================== */
/* 精确禁用Element Plus组件的transition和animation，但保留ECharts动画 */
.el-card:not(.chart-card),
.el-card:not(.chart-card) *:not(canvas):not(svg):not(.chart-container):not(.chart-container *),
.el-button:not(.chart-container .el-button),
.el-button:not(.chart-container .el-button) *:not(canvas):not(svg),
.el-select:not(.chart-container .el-select),
.el-select:not(.chart-container .el-select) *:not(canvas):not(svg),
.el-input:not(.chart-container .el-input),
.el-input:not(.chart-container .el-input) *:not(canvas):not(svg),
.el-date-picker:not(.chart-container .el-date-picker),
.el-date-picker:not(.chart-container .el-date-picker) *:not(canvas):not(svg),
.el-button-group:not(.chart-container .el-button-group),
.el-button-group:not(.chart-container .el-button-group) *:not(canvas):not(svg),
.el-dropdown,
.el-dropdown *:not(canvas):not(svg),
.el-tooltip,
.el-tooltip *:not(canvas):not(svg),
.el-row,
.el-row *:not(canvas):not(svg):not(.chart-container):not(.chart-container *),
.el-col,
.el-col *:not(canvas):not(svg):not(.chart-container):not(.chart-container *),
.el-form,
.el-form *:not(canvas):not(svg),
.el-form-item,
.el-form-item *:not(canvas):not(svg),
.el-dialog,
.el-dialog *:not(canvas):not(svg),
.el-loading-mask,
.el-loading-mask *:not(canvas):not(svg),
.el-menu,
.el-menu *:not(canvas):not(svg),
.el-submenu,
.el-submenu *:not(canvas):not(svg),
.el-drawer,
.el-drawer *:not(canvas):not(svg),
.el-radio,
.el-radio *:not(canvas):not(svg),
.el-checkbox,
.el-checkbox *:not(canvas):not(svg),
.el-switch,
.el-switch *:not(canvas):not(svg),
.el-slider,
.el-slider *:not(canvas):not(svg),
.el-progress,
.el-progress *:not(canvas):not(svg),
.el-badge,
.el-badge *:not(canvas):not(svg),
.el-tag,
.el-tag *:not(canvas):not(svg),
.el-alert,
.el-alert *:not(canvas):not(svg),
.el-message,
.el-message *:not(canvas):not(svg),
.el-notification,
.el-notification *:not(canvas):not(svg),
.el-popover,
.el-popover *:not(canvas):not(svg),
.el-cascader,
.el-cascader *:not(canvas):not(svg),
.el-color-picker,
.el-color-picker *:not(canvas):not(svg),
.el-transfer,
.el-transfer *:not(canvas):not(svg),
.el-container,
.el-header,
.el-aside,
.el-main,
.el-footer,
.el-scrollbar,
.el-scrollbar *:not(canvas):not(svg),
.el-carousel,
.el-carousel *:not(canvas):not(svg),
.el-collapse,
.el-collapse *:not(canvas):not(svg),
.el-timeline,
.el-timeline *:not(canvas):not(svg),
.el-divider,
.el-divider *:not(canvas):not(svg),
.el-calendar,
.el-calendar *:not(canvas):not(svg),
.el-image,
.el-image *:not(canvas):not(svg),
.el-backtop,
.el-backtop *:not(canvas):not(svg),
.el-page-header,
.el-page-header *:not(canvas):not(svg),
.el-breadcrumb,
.el-breadcrumb *:not(canvas):not(svg),
.el-pagination,
.el-pagination *:not(canvas):not(svg),
.el-avatar,
.el-avatar *:not(canvas):not(svg),
.el-empty,
.el-empty *:not(canvas):not(svg),
.el-descriptions,
.el-descriptions *:not(canvas):not(svg),
.el-result,
.el-result *:not(canvas):not(svg),
.el-skeleton,
.el-skeleton *:not(canvas):not(svg),
.el-statistic,
.el-statistic *:not(canvas):not(svg),
.el-affix,
.el-affix *:not(canvas):not(svg),
.el-anchor,
.el-anchor *:not(canvas):not(svg),
.el-tour,
.el-tour *:not(canvas):not(svg),
.el-watermark,
.el-watermark *:not(canvas):not(svg),
.el-config-provider,
.el-config-provider *:not(canvas):not(svg),
.el-space,
.el-space *:not(canvas):not(svg),
.el-auto-resizer,
.el-auto-resizer *:not(canvas):not(svg),
.el-check-tag,
.el-check-tag *:not(canvas):not(svg),
.el-text,
.el-text *:not(canvas):not(svg),
.el-tree-select,
.el-tree-select *:not(canvas):not(svg),
.el-table-v2,
.el-table-v2 *:not(canvas):not(svg),
.el-tree-v2,
.el-tree-v2 *:not(canvas):not(svg),
.el-segmented,
.el-segmented *:not(canvas):not(svg),
.el-mention,
.el-mention *:not(canvas):not(svg),
.el-flex,
.el-flex *:not(canvas):not(svg),
.el-radio-group,
.el-radio-group *:not(canvas):not(svg),
.el-radio-button,
.el-radio-button *:not(canvas):not(svg) {
    transition: none !important;
    animation: none !important;
    -webkit-transition: none !important;
    -moz-transition: none !important;
    -o-transition: none !important;
    -webkit-animation: none !important;
    -moz-animation: none !important;
    -o-animation: none !important;
}

/* =================== 基础样式 =================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
}

#app {
    height: 100vh;
    width: 100vw;
}

/* =================== 容器布局 =================== */
.dashboard-container {
    height: 100vh;
    width: 100vw;
    background: transparent;
}

.dashboard-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    height: 80px !important;
}

.header-left {
    display: flex;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 16px;
}

.logo {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 8px;
    backdrop-filter: blur(5px);
}

.title-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.title {
    color: white !important;
    font-weight: 600;
    font-size: 24px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.datetime-subtitle {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 14px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.enter-btn {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    backdrop-filter: blur(5px);
    font-weight: 500;
}

.enter-btn:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* =================== 主内容区域 =================== */
.dashboard-main {
    padding: 24px;
    height: calc(100vh - 80px);
    overflow-y: auto;
    background: transparent;
}

/* =================== 图表卡片 =================== */
.chart-card {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    height: 100%;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
}

.header-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.chart-title {
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
}

.chart-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.chart-container {
    width: 100%;
    height: 400px;  /* 大幅增加高度，解决柱状图压缩问题 */
}

/* =================== 统计面板样式 =================== */
.chart-content-wrapper {
    display: flex;
    align-items: stretch;
    flex: 1;
    gap: 12px;
    overflow: hidden;
    min-height: 280px;
    width: 100%;
    box-sizing: border-box;
}

.stats-panel {
    flex: 0 0 160px;
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 8px 0;
    min-height: 0;
    align-items: stretch;
    justify-content: space-evenly;
    align-self: stretch;
    overflow: hidden;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 1px;
    padding: 6px 8px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border: 1px solid rgba(220, 223, 230, 0.6);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: none;
    flex: 1;
    min-height: 0;
    max-height: none;
    align-items: center;
    justify-content: center;
    text-align: center;
    min-height: 0;
    overflow: hidden;
}

.stat-item:hover {
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.stat-label {
    font-size: 11px;
    color: #666;
    margin: 0;
    padding: 0;
    line-height: 1.2;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin: 0;
    padding: 0;
    line-height: 1.1;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.stat-value.total-checks {
    color: #409EFF;
}

.stat-value.closed-issues {
    color: #67C23A;
}

.stat-value.found-issues {
    color: #F56C6C;
}

.stat-value.pending-issues {
    color: #E6A23C;
}

/* 调整饼图容器 */
.chart-content-wrapper .chart-container {
    flex: 1;
    min-height: 200px;
    max-height: 100%;
    align-self: stretch;
    overflow: hidden;
    box-sizing: border-box;
}

/* =================== 统计卡片 =================== */
.stats-row {
    margin-top: 24px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.stat-content {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 8px;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.stat-icon.total {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon.completed {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-icon.abnormal {
    background: linear-gradient(135deg, #fa709a, #fee140);
}

.stat-icon.note {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: #7f8c8d;
    margin-top: 4px;
}

/* =================== 响应式设计 =================== */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 0 16px;
        height: 70px !important;
    }

    .title {
        font-size: 20px;
    }

    .dashboard-main {
        padding: 16px;
        height: calc(100vh - 70px);
    }

    .chart-container {
        height: 250px;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .chart-controls {
        width: 100%;
        justify-content: flex-start;
    }

    /* 移动端统计面板调整 */
    .chart-content-wrapper {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .stats-panel {
        flex: none;
        flex-direction: row;
        justify-content: space-around;
        gap: 8px;
    }

    .stat-item {
        flex: 1;
        padding: 6px 8px;
        text-align: center;
    }
}
