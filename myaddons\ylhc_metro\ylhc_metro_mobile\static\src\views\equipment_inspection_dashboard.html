<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备巡检数据看板</title>
    <link rel="stylesheet" href="/ylhc_metro_mobile/static/src/views/equipment_inspection_dashboard.css">
    <script src="/ylhc_metro_mobile/static/src/lib/echarts.min.js"></script>
    <!-- Element Plus 样式 -->
    <link rel="stylesheet" href="/ylhc_metro_mobile/static/src/lib/element-plus.css" />
    <!-- Vue3 -->
    <script src="/ylhc_metro_mobile/static/src/lib/vue.global.prod.js"></script>
    <!-- Element Plus JS -->
    <script src="/ylhc_metro_mobile/static/src/lib/element-plus.js"></script>
    <!-- Element Plus Icons -->
    <script src="/ylhc_metro_mobile/static/src/lib/element-plus-icons.min.js"></script>
    <!-- Element Plus 中文语言包 -->
    <script src="/ylhc_metro_mobile/static/src/lib/zh-cn.min.js"></script>
</head>
<body data-loading="true">
    <div id="app">
        <!-- 顶部导航栏 -->
        <el-container class="dashboard-container">
            <el-header class="dashboard-header">
                <div class="header-left">
                    <div class="logo-section">
                        <el-icon class="logo" :size="36" color="white">
                            <Tools />
                        </el-icon>
                        <div class="title-group">
                            <el-text class="title" size="large" tag="h1">设备巡检数据看板</el-text>
                            <el-text class="datetime-subtitle" size="small">{{ currentDate }} {{ currentTime }}</el-text>
                        </div>
                    </div>
                </div>
                <div class="header-right">
                    <el-button type="success" @click="enterSystem" round size="large" class="enter-btn">
                        <el-icon style="margin-right: 6px;"><Right /></el-icon>
                        进入系统
                    </el-button>
                </div>
            </el-header>

            <!-- 主要内容区域 -->
            <el-main class="dashboard-main">
                <!-- 图表网格布局 -->
                <el-row :gutter="16" class="charts-grid"
                        style="height: 100%; margin: 0; width: 100%; box-sizing: border-box;"
                        justify="space-between" align="stretch"
                >
                    <!-- 巡检专业分布 -->
                    <el-col :span="12" :xs="24" :sm="12" style="width: 50%; box-sizing: border-box;">
                        <el-card class="chart-card" shadow="hover" :body-style="{ padding: '16px', width: '100%', boxSizing: 'border-box' }">
                            <template #header>
                                <div class="card-header">
                                    <div class="header-content">
                                        <el-text class="chart-title" size="large" tag="h3">
                                            <el-icon style="margin-right: 8px;" color="#409EFF">
                                                <Tools />
                                            </el-icon>
                                            巡检专业分布
                                        </el-text>
                                        <el-text v-if="currentDataScope" size="small" type="info" tag="span">
                                            {{ currentDataScope }}
                                        </el-text>
                                        <el-text v-else size="small" type="info" tag="span">
                                            全部数据
                                        </el-text>
                                    </div>
                                    <div class="chart-controls">
                                        <!-- 下钻返回按钮 -->
                                        <el-button
                                            v-if="showDrillBackBtn"
                                            type="primary"
                                            @click="handleDrillBack"
                                            size="small"
                                            style="margin-right: 8px;"
                                        >
                                            <el-icon style="margin-right: 4px;"><Tools /></el-icon>
                                            <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
                                            返回
                                        </el-button>
                                        <!-- 线路筛选 -->
                                        <el-select
                                            v-model="selectedLine"
                                            placeholder="全部线路"
                                            style="width: 140px; margin-right: 8px;"
                                            @change="onLineChange"
                                            clearable
                                            size="default"
                                        >
                                            <el-option
                                                v-for="line in lineOptions"
                                                :key="line.value"
                                                :label="line.label"
                                                :value="line.value"
                                            />
                                        </el-select>
                                        <!-- 专业筛选 -->
                                        <el-select
                                            v-model="selectedMajor"
                                            placeholder="全部专业"
                                            style="width: 140px; margin-right: 8px;"
                                            @change="onMajorChange"
                                            clearable
                                            size="default"
                                        >
                                            <el-option
                                                v-for="major in majorOptions"
                                                :key="major.value"
                                                :label="major.label"
                                                :value="major.value"
                                            />
                                        </el-select>
                                        <!-- 月份筛选 -->
                                        <el-date-picker
                                            v-model="selectedMonth"
                                            type="month"
                                            placeholder="选择月份"
                                            format="YYYY-MM"
                                            value-format="YYYY-MM"
                                            @change="onMonthChange"
                                            style="width: 140px;"
                                            size="default"
                                            clearable
                                        />
                                    </div>
                                </div>
                            </template>
                            <div class="chart-content-wrapper">
                                <!-- 左侧统计信息面板 -->
                                <div class="stats-panel">
                                    <div class="stat-item">
                                        <div class="stat-label">总巡检数:</div>
                                        <div class="stat-value total-checks">{{ dashboardData.total_count || 0 }}</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">已完成:</div>
                                        <div class="stat-value closed-issues">{{ dashboardData.completed_count || 0 }}</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">异常数:</div>
                                        <div class="stat-value found-issues">{{ dashboardData.abnormal_count || 0 }}</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">有备注:</div>
                                        <div class="stat-value pending-issues">{{ dashboardData.has_note_count || 0 }}</div>
                                    </div>
                                </div>
                                <!-- 右侧饼图 -->
                                <div id="majorChart" class="chart-container"
                                     style="width: calc(100% - 172px); min-width: 200px; min-height: 280px; box-sizing: border-box;">
                                </div>
                            </div>
                        </el-card>
                    </el-col>

                    <!-- 系统分布 -->
                    <el-col :span="12" :xs="24" :sm="12" style="width: 50%; box-sizing: border-box;">
                        <el-card class="chart-card" shadow="hover" :body-style="{ padding: '16px', width: '100%', boxSizing: 'border-box' }">
                            <template #header>
                                <div class="card-header">
                                    <div class="header-content">
                                        <el-text class="chart-title" size="large" tag="h3">
                                            <el-icon style="margin-right: 8px;" color="#67C23A">
                                                <Setting />
                                            </el-icon>
                                            系统分布
                                        </el-text>
                                    </div>
                                </div>
                            </template>
                            <div id="systemChart" class="chart-container"
                                 style="width: 100%; min-height: 280px; box-sizing: border-box;">
                            </div>
                        </el-card>
                    </el-col>

                    <!-- 站点巡检统计 -->
                    <el-col :span="12" :xs="24" :sm="12" style="width: 50%; box-sizing: border-box;">
                        <el-card class="chart-card" shadow="hover" :body-style="{ padding: '16px', width: '100%', boxSizing: 'border-box' }">
                            <template #header>
                                <div class="card-header">
                                    <div class="header-content">
                                        <el-text class="chart-title" size="large" tag="h3">
                                            <el-icon style="margin-right: 8px;" color="#E6A23C">
                                                <TrendCharts />
                                            </el-icon>
                                            站点巡检统计TOP10
                                        </el-text>
                                        <el-text size="small" type="info">各站点巡检总次数统计</el-text>
                                    </div>
                                </div>
                            </template>
                            <div id="stationChart" class="chart-container"
                                 style="width: 100%; min-height: 280px; box-sizing: border-box;">
                            </div>
                        </el-card>
                    </el-col>

                    <!-- 站点异常统计 -->
                    <el-col :span="12" :xs="24" :sm="12" style="width: 50%; box-sizing: border-box;">
                        <el-card class="chart-card" shadow="hover" :body-style="{ padding: '16px', width: '100%', boxSizing: 'border-box' }">
                            <template #header>
                                <div class="card-header">
                                    <div class="header-content">
                                        <el-text class="chart-title" size="large" tag="h3">
                                            <el-icon style="margin-right: 8px;" color="#F56C6C">
                                                <Warning />
                                            </el-icon>
                                            站点异常统计TOP10
                                        </el-text>
                                        <el-text size="small" type="info">各站点巡检异常次数统计</el-text>
                                    </div>
                                </div>
                            </template>
                            <div id="issueChart" class="chart-container"
                                 style="width: 100%; min-height: 280px; box-sizing: border-box;">
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
            </el-main>
        </el-container>
    </div>

    <script src="/ylhc_metro_mobile/static/src/views/equipment_inspection_dashboard.js"></script>
</body>
</html>
