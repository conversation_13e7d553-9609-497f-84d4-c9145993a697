<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备巡检数据看板</title>
    <link rel="stylesheet" href="/ylhc_metro_mobile/static/src/views/equipment_inspection_dashboard.css">
    <script src="/ylhc_metro_mobile/static/src/lib/echarts.min.js"></script>
    <!-- Element Plus 样式 -->
    <link rel="stylesheet" href="/ylhc_metro_mobile/static/src/lib/element-plus.css" />
    <!-- Vue3 -->
    <script src="/ylhc_metro_mobile/static/src/lib/vue.global.prod.js"></script>
    <!-- Element Plus JS -->
    <script src="/ylhc_metro_mobile/static/src/lib/element-plus.js"></script>
    <!-- Element Plus Icons -->
    <script src="/ylhc_metro_mobile/static/src/lib/element-plus-icons.min.js"></script>
    <!-- Element Plus 中文语言包 -->
    <script src="/ylhc_metro_mobile/static/src/lib/zh-cn.min.js"></script>
</head>
<body data-loading="true">
    <div id="app">
        <!-- 顶部导航栏 -->
        <el-container class="dashboard-container">
            <el-header class="dashboard-header">
                <div class="header-left">
                    <div class="logo-section">
                        <el-icon class="logo" :size="36" color="white">
                            <Tools />
                        </el-icon>
                        <div class="title-group">
                            <el-text class="title" size="large" tag="h1">设备巡检数据看板</el-text>
                            <el-text class="datetime-subtitle" size="small">{{ currentDate }} {{ currentTime }}</el-text>
                        </div>
                    </div>
                </div>
                <div class="header-right">
                    <el-button type="success" @click="enterSystem" round size="large" class="enter-btn">
                        <el-icon style="margin-right: 6px;"><Right /></el-icon>
                        进入系统
                    </el-button>
                </div>
            </el-header>

            <!-- 主要内容区域 -->
            <el-main class="dashboard-main">
                <!-- 图表网格布局 -->
                <el-row :gutter="16" class="charts-grid" 
                        style="height: 100%; margin: 0; width: 100%; box-sizing: border-box;"
                        justify="space-between" align="stretch"
                >
                    <!-- 巡检专业分布 -->
                    <el-col :span="12" :xs="24" :sm="12" style="width: 50%; box-sizing: border-box;">
                        <el-card class="chart-card" shadow="hover" :body-style="{ padding: '16px', width: '100%', boxSizing: 'border-box' }">
                            <template #header>
                                <div class="card-header">
                                    <div class="header-content">
                                        <el-text class="chart-title" size="large" tag="h3">
                                            <el-icon style="margin-right: 8px;" color="#409EFF">
                                                <Tools />
                                            </el-icon>
                                            巡检专业分布
                                        </el-text>
                                        <el-text v-if="currentDataScope" size="small" type="info" tag="span">
                                            {{ currentDataScope }}
                                        </el-text>
                                        <el-text v-else size="small" type="info" tag="span">
                                            全部数据
                                        </el-text>
                                    </div>
                                    <div class="chart-controls">
                                        <!-- 下钻返回按钮 -->
                                        <el-button
                                            v-if="showDrillBackBtn"
                                            type="primary"
                                            @click="handleDrillBack"
                                            size="small"
                                            style="margin-right: 8px;"
                                        >
                                            <el-icon style="margin-right: 4px;"><Tools /></el-icon>
                                            <el-icon style="margin-right: 4px;"><ArrowLeft /></el-icon>
                                            返回
                                        </el-button>
                                        <!-- 线路筛选 -->
                                        <el-select
                                            v-model="selectedLine"
                                            placeholder="全部线路"
                                            style="width: 140px; margin-right: 8px;"
                                            @change="onLineChange"
                                            clearable
                                            size="default"
                                        >
                                            <el-option
                                                v-for="line in lineOptions"
                                                :key="line.value"
                                                :label="line.label"
                                                :value="line.value"
                                            />
                                        </el-select>
                                        <!-- 专业筛选 -->
                                        <el-select
                                            v-model="selectedMajor"
                                            placeholder="全部专业"
                                            style="width: 140px; margin-right: 8px;"
                                            @change="onMajorChange"
                                            clearable
                                            size="default"
                                        >
                                            <el-option
                                                v-for="major in majorOptions"
                                                :key="major.value"
                                                :label="major.label"
                                                :value="major.value"
                                            />
                                        </el-select>
                                        <!-- 月份筛选 -->
                                        <el-date-picker
                                            v-model="selectedMonth"
                                            type="month"
                                            placeholder="选择月份"
                                            format="YYYY-MM"
                                            value-format="YYYY-MM"
                                            @change="onMonthChange"
                                            style="width: 140px;"
                                            size="default"
                                            clearable
                                        />
                                    </div>
                                </div>
                            </template>
                            <div class="chart-container" id="majorChart" style="width: 100%; height: 300px;"></div>
                        </el-card>
                    </el-col>

                    <!-- 系统分布 -->
                    <el-col :span="12" :xs="24" :sm="12" style="width: 50%; box-sizing: border-box;">
                        <el-card class="chart-card" shadow="hover" :body-style="{ padding: '16px', width: '100%', boxSizing: 'border-box' }">
                            <template #header>
                                <div class="card-header">
                                    <div class="header-content">
                                        <el-text class="chart-title" size="large" tag="h3">
                                            <el-icon style="margin-right: 8px;" color="#67C23A">
                                                <Setting />
                                            </el-icon>
                                            系统分布
                                        </el-text>
                                    </div>
                                </div>
                            </template>
                            <div class="chart-container" id="systemChart" style="width: 100%; height: 300px;"></div>
                        </el-card>
                    </el-col>
                </el-row>

                <!-- 第二行图表 -->
                <el-row :gutter="16" class="charts-grid" style="margin-top: 16px;">
                    <!-- 站点巡检统计 -->
                    <el-col :span="12" :xs="24" :sm="12">
                        <el-card class="chart-card" shadow="hover">
                            <template #header>
                                <div class="card-header">
                                    <div class="header-content">
                                        <el-text class="chart-title" size="large" tag="h3">
                                            <el-icon style="margin-right: 8px;" color="#E6A23C">
                                                <Location />
                                            </el-icon>
                                            站点巡检统计
                                        </el-text>
                                    </div>
                                </div>
                            </template>
                            <div class="chart-container" id="stationChart" style="width: 100%; height: 300px;"></div>
                        </el-card>
                    </el-col>

                    <!-- 站点异常统计 -->
                    <el-col :span="12" :xs="24" :sm="12">
                        <el-card class="chart-card" shadow="hover">
                            <template #header>
                                <div class="card-header">
                                    <div class="header-content">
                                        <el-text class="chart-title" size="large" tag="h3">
                                            <el-icon style="margin-right: 8px;" color="#F56C6C">
                                                <Warning />
                                            </el-icon>
                                            站点异常统计
                                        </el-text>
                                    </div>
                                </div>
                            </template>
                            <div class="chart-container" id="issueChart" style="width: 100%; height: 300px;"></div>
                        </el-card>
                    </el-col>
                </el-row>

                <!-- 第三行 - 趋势分析 -->
                <el-row :gutter="16" class="charts-grid" style="margin-top: 16px;">
                    <el-col :span="24">
                        <el-card class="chart-card" shadow="hover">
                            <template #header>
                                <div class="card-header">
                                    <div class="header-content">
                                        <el-text class="chart-title" size="large" tag="h3">
                                            <el-icon style="margin-right: 8px;" color="#909399">
                                                <TrendCharts />
                                            </el-icon>
                                            巡检趋势分析
                                        </el-text>
                                    </div>
                                    <div class="chart-controls">
                                        <el-radio-group v-model="trendViewMode" @change="onTrendViewModeChange" size="small">
                                            <el-radio-button value="daily">按日</el-radio-button>
                                            <el-radio-button value="weekly">按周</el-radio-button>
                                        </el-radio-group>
                                    </div>
                                </div>
                            </template>
                            <div class="chart-container" id="trendChart" style="width: 100%; height: 400px;"></div>
                        </el-card>
                    </el-col>
                </el-row>

                <!-- 统计卡片 -->
                <el-row :gutter="16" class="stats-row" style="margin-top: 16px;">
                    <el-col :span="6" :xs="12" :sm="6">
                        <el-card class="stat-card" shadow="hover">
                            <div class="stat-content">
                                <div class="stat-icon total">
                                    <el-icon :size="32"><Document /></el-icon>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-number">{{ dashboardData.total_count || 0 }}</div>
                                    <div class="stat-label">总巡检数</div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="6" :xs="12" :sm="6">
                        <el-card class="stat-card" shadow="hover">
                            <div class="stat-content">
                                <div class="stat-icon completed">
                                    <el-icon :size="32"><CircleCheck /></el-icon>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-number">{{ dashboardData.completed_count || 0 }}</div>
                                    <div class="stat-label">已完成</div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="6" :xs="12" :sm="6">
                        <el-card class="stat-card" shadow="hover">
                            <div class="stat-content">
                                <div class="stat-icon abnormal">
                                    <el-icon :size="32"><Warning /></el-icon>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-number">{{ dashboardData.abnormal_count || 0 }}</div>
                                    <div class="stat-label">异常数</div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="6" :xs="12" :sm="6">
                        <el-card class="stat-card" shadow="hover">
                            <div class="stat-content">
                                <div class="stat-icon note">
                                    <el-icon :size="32"><EditPen /></el-icon>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-number">{{ dashboardData.has_note_count || 0 }}</div>
                                    <div class="stat-label">有备注</div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
            </el-main>
        </el-container>
    </div>

    <script src="/ylhc_metro_mobile/static/src/views/equipment_inspection_dashboard.js"></script>
</body>
</html>
