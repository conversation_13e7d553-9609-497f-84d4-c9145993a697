<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <data>
        <!-- 搜索视图 -->
        <record id="equipment_inspection_view_search" model="ir.ui.view">
            <field name="name">equipment_inspection_search</field>
            <field name="model">equipment.inspection</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name"/>
                    <field name="line_id"/>
                    <field name="location_id"/>
                    <field name="major_id"/>
                    <field name="type_id"/>
                    <field name="inspection_person_ids"/>
                    <field name="inspection_date"/>
                    <filter string="草稿" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="进行中" name="in_progress" domain="[('state', '=', 'in_progress')]"/>
                    <filter string="已完成" name="done" domain="[('state', '=', 'done')]"/>
                    <filter string="今日" name="today" domain="[('inspection_date', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter string="本周" name="this_week" domain="[('inspection_date', '>=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')), ('inspection_date', '&lt;=', (context_today() + datetime.timedelta(days=6-context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="分组">
                        <filter string="线路" name="group_line" context="{'group_by': 'line_id'}"/>
                        <filter string="站点" name="group_location" context="{'group_by': 'location_id'}"/>
                        <filter string="专业" name="group_major" context="{'group_by': 'major_id'}"/>
                        <filter string="状态" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="日期" name="group_date" context="{'group_by': 'inspection_date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- 列表视图 -->
        <record id="equipment_inspection_view_list" model="ir.ui.view">
            <field name="name">equipment_inspection_list</field>
            <field name="model">equipment.inspection</field>
            <field name="arch" type="xml">
                <list>
                    <field name="name"/>
                    <field name="line_id"/>
                    <field name="location_id"/>
                    <field name="major_id"/>
                    <field name="inspection_date"/>
                    <field name="inspection_person_ids" widget="many2many_tags"/>
                    <field name="state" widget="badge" decoration-success="state == 'done'" decoration-info="state == 'in_progress'" decoration-muted="state == 'draft'" decoration-danger="state == 'cancel'"/>
                    <field name="total_items"/>
                    <field name="completed_items"/>
                    <field name="completion_rate" widget="progressbar"/>
                </list>
            </field>
        </record>

        <!-- 表单视图 -->
        <record id="equipment_inspection_view_form" model="ir.ui.view">
            <field name="name">equipment_inspection_form</field>
            <field name="model">equipment.inspection</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <field name="state" widget="statusbar" statusbar_visible="draft,in_progress,done"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button type="object" name="action_view_attachments" class="oe_stat_button" icon="fa-files-o">
                                <field name="total_items" widget="statinfo" string="总项目数"/>
                            </button>
                            <button type="object" name="action_view_completed" class="oe_stat_button" icon="fa-check">
                                <field name="completed_items" widget="statinfo" string="已完成"/>
                            </button>
                            <button type="object" name="action_view_abnormal" class="oe_stat_button" icon="fa-exclamation-triangle">
                                <field name="abnormal_items" widget="statinfo" string="异常项目"/>
                            </button>
                        </div>
                        
                        <group>
                            <group>
                                <field name="name" readonly="1"/>
                                <field name="line_id" readonly="1"/>
                                <field name="location_id" readonly="1"/>
                                <field name="inspection_date" readonly="1"/>
                                <field name="major_id" readonly="1"/>
                            </group>
                            <group>
                                <field name="type_id" readonly="1"/>
                                <field name="start_time" readonly="1"/>
                                <field name="end_time" readonly="1"/>
                                <field name="inspection_person_ids" widget="many2many_tags" readonly="1"/>
                                <field name="completion_rate" widget="progressbar"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="巡检项目" name="inspection_items">
                                <field name="inspection_system_ids" mode="list">
                                    <list>
                                        <field name="system_id"/>
                                        <field name="subsystem_count"/>
                                        <field name="completed_count"/>
                                        <field name="abnormal_count"/>
                                        <button name="action_view_subsystems" 
                                                type="object" 
                                                string="查看子项" 
                                                icon="fa-list" 
                                                class="btn-link"/>
                                    </list>
                                </field>
                            </page>
                            <page string="备注" name="notes">
                                <field name="note" placeholder="填写巡检备注..."/>
                            </page>
                        </notebook>
                    </sheet>
                    <chatter/>
                </form>
            </field>
        </record>

        <!-- 透视图视图 -->
        <record id="equipment_inspection_view_pivot" model="ir.ui.view">
            <field name="name">equipment_inspection_pivot</field>
            <field name="model">equipment.inspection</field>
            <field name="arch" type="xml">
                <pivot string="设备巡检分析">
                    <field name="inspection_person_ids" type="row"/>
                    <field name="line_id" type="row"/>
                    <field name="location_id" type="row"/>
                    <field name="major_id" type="row"/>
                    <field name="type_id" type="row"/>
                    <field name="inspection_date" type="col" interval="month"/>
                    <field name="state" type="col"/>
                    <field name="total_items" type="measure"/>
                    <field name="completed_items" type="measure"/>
                    <field name="abnormal_items" type="measure"/>
                    <field name="completion_rate" type="measure"/>
                </pivot>
            </field>
        </record>

        <!-- 动作窗口 -->
        <record id="equipment_inspection_action_window" model="ir.actions.act_window">
            <field name="name">设备巡检</field>
            <field name="res_model">equipment.inspection</field>
            <field name="view_mode">list,form</field>
            <field name="path">equipment-inspection</field>
            <field name="search_view_id" ref="equipment_inspection_view_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    创建您的第一个设备巡检记录
                </p>
                <p>
                    选择巡检专业后，系统将自动展开对应的设备和子项供您检查。
                </p>
            </field>
        </record>

        <!-- 设备巡检数据看板动作 -->
        <record id="equipment_inspection_dashboard_action_url" model="ir.actions.act_url">
            <field name="name">📊 设备巡检数据看板</field>
            <field name="url">/equipment_inspection/dashboard</field>
            <field name="target">self</field>
        </record>

        <!-- 透视图动作窗口 -->
        <record id="equipment_inspection_pivot_action" model="ir.actions.act_window">
            <field name="name">设备巡检透视图</field>
            <field name="res_model">equipment.inspection</field>
            <field name="view_mode">pivot</field>
            <field name="view_id" ref="equipment_inspection_view_pivot"/>
        </record>

        <!-- 菜单项 -->
        <menuitem id="equipment_inspection_action_window_menu"
                  parent="ylhc_metro_mobile_inspection_new"
                  action="equipment_inspection_action_window"
                  name="设备巡检" sequence="10"/>

        <!-- 设备巡检数据看板菜单项 -->
        <menuitem id="equipment_inspection_dashboard_menu"
                  parent="ylhc_metro_mobile_inspection_report_new"
                  action="equipment_inspection_dashboard_action_url"
                  name="📊 设备巡检数据看板"
                  sequence="1"/>

        <!-- 设备巡检透视图菜单项 -->
        <menuitem id="equipment_inspection_pivot_menu"
                  parent="ylhc_metro_mobile_inspection_report_new"
                  action="equipment_inspection_pivot_action"
                  name="📈 设备巡检透视图"
                  sequence="2"/>
    </data>
</odoo>
