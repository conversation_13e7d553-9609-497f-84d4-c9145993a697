# -*- coding: utf-8 -*-
import json
import requests
import logging

_logger = logging.getLogger(__name__)
from bs4 import BeautifulSoup

from odoo import models, fields, api
from odoo.exceptions import UserError


from .utils.yh_tool.yh_main import ICKey, WordPage
from .utils.jlc_tool.jlc_tool import Ali, Jtc


class YlhcProduct(models.Model):
    """
    extend product.pricelist
    """
    _inherit = 'product.template'

    price_list_item_ids = fields.One2many(
        comodel_name='product.pricelist.item',
        inverse_name='product_tmpl_id',
        string='Pricelist Items'
    )

    variant_price_list_item_ids = fields.One2many(
        comodel_name='product.pricelist.item',
        inverse_name='product_tmpl_id',
        string='Variant Pricelist Items'
    )

    def action_product_variant_view(self):

        action = self.env['ir.actions.actions']._for_xml_id('stock.stock_product_normal_action')
        if len(self.product_variant_ids) > 1:
            action['domain'] = [('id', 'in', self.product_variant_ids.ids)]
        elif len(self.product_variant_ids) == 1:
            form_view = [(self.env.ref('product.product_normal_form_view').id, 'form')]
            if 'views' in action:
                action['views'] = form_view + [(state, view) for state, view in action['views'] if view != 'form']
            else:
                action['views'] = form_view
            action['res_id'] = self.product_variant_ids.id
        else:
            action = {'type': 'ir.actions.act_window_close'}

        return action

    @api.model_create_multi
    def create(self, vals_list):
        """
        create the product and the pricelist item
        """
        products = super(YlhcProduct, self).create(vals_list)

        for product in products:

            for price_list_item_id in product.price_list_item_ids:
                price_list_item_id.check_price_list()

            for variant_price_list_item_id in product.variant_price_list_item_ids:
                variant_price_list_item_id.check_price_list()

        return products

    def write(self, vals):
        """
        Update the product and the pricelist item
        """
        res = super(YlhcProduct, self).write(vals)
        for product in self:
            for price_list_item_id in product.price_list_item_ids:
                price_list_item_id.check_price_list()
            for variant_price_list_item_id in product.variant_price_list_item_ids:
                variant_price_list_item_id.check_price_list()
        return res


class YlhcProductProduct(models.Model):
    _inherit = 'product.product'

    offsite_count = fields.Integer(compute="_compute_offsite_count", string="站外查询网站总数")


    def batch_is_published(self):
        """
        批量发布产品到电商网站
        :return:
        """
        for product in self:
            product.is_published = True
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': '电商网站',
                'message': '产品已批量发布到电商网站',
                'sticky': False,
                'type': 'success',
            }
        }

    def batch_cancel_is_published(self):
        """
        批量取消发布产品到电商网站
        :return:
        """
        for product in self:
            product.is_published = False
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': '电商网站',
                'message': '产品已批量取消发布到电商网站',
                'sticky': False,
                'type': 'success',
            }
        } 

    def _get_category_website(self, field_name, website_desc):
        """
        递归查找产品分类的指定网站字段
        :param field_name: 字段名，如 'yunhan_website_id'
        :param website_desc: 网站描述，如 '云汉芯城'
        :return: 网站对象
        """
        category = self.categ_id
        while category:
            website = getattr(category, field_name, None)
            if website:
                return website
            category = category.parent_id
        raise UserError(f"请先配置产品分类的{website_desc}网站字段")

    def _compute_offsite_count(self):
        """
        递归统计产品关联的站外查询网站数量（每种类型只统计一次）
        :return:
        """
        def find_website(category, field_name):
            while category:
                website = getattr(category, field_name, None)
                if website:
                    return True
                category = category.parent_id
            return False

        for product in self:
            count = 0
            if find_website(product.categ_id, 'jbchip_website_id'):
                count += 1
            if find_website(product.categ_id, 'youhuo_website_id'):
                count += 1
            if find_website(product.categ_id, 'jlc_website_id'):
                count += 1
            if find_website(product.categ_id, 'yunhan_website_id'):
                count += 1
            product.offsite_count = count

    def product_price_inquiry(self):
        """
        京北商城数据获取（递归查找分类配置）
        :return:
        """
        offsite = self._get_category_website('jbchip_website_id', '京北商城')
        url = "https://www.jbchip.com/api/item/goods/loadGoodsByQuery"

        def _get_token():
            return offsite.login_save_param.get('data').get('access_token')

        # 判断 token 是否过期
        if fields.Datetime.now() >= offsite.token_expire_at:
            offsite.login_website()

        def _send_query(token):
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "Mozilla/5.0",
                "Accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "locale": "zh_cn",
                "ipaddr": "true",
            }

            payload = {
                "current": 1,
                "size": 10,
                "sort": "goodsNumber.keyword",
                "sortFlag": "DESC",
                "searchField": self.name,
                "searchFieldType": 0,
                "imagesFlag": 0,
                "documentsFlag": None,
                "isProductDetailSearch": True,
                "conditions": None,
                "userSource": "0",
            }

            try:
                response = requests.post(url, json=payload, headers=headers, timeout=10)
                if response.status_code == 200:
                    return response.json()
                logging.info(f"[{offsite.name}] 状态码 {response.status_code}：{response.text[:300]}")
            except Exception as e:
                logging.info(f"[{offsite.name}] 请求失败：{str(e)}")

        # 第一次请求
        data = _send_query(_get_token())

        # 若返回 401 再登录并重试一次
        if data.get('code') == 401:
            offsite.login_website()
            data = _send_query(_get_token())

        return {'source_website_id': offsite.id, 'data': data}

    def jbchip_product_price_inquiry(self):
        """
        京北商城数据处理
        :return:
        """
        # 调用价格查询API获取数据
        source_data = self.product_price_inquiry()
        data = source_data.get('data').get('data')
        records = [item for item in data.get('records', []) if item["goodsName"] == self.name]
        website_inventory = 0.0
        # 准备向导行数据
        line_ids = []

        # 处理每个返回的记录
        for record in records:
            # 1. 添加批次信息
            if dc := record.get('dc'):
                line_ids.append((0, 0, {
                    'code':'dc',
                    'key': '批次',
                    'value': dc,
                    'type': 'lots',
                    'offsite_id': source_data.get('source_website_id')
                }))

            # 2. 添加包装信息
            if packaging := record.get('goodsBaozhuang'):
                line_ids.append((0, 0, {
                    'code': 'goodsBaozhuang',
                    'key': '包装',
                    'value': packaging,
                    'type': 'package',
                    'offsite_id': source_data.get('source_website_id')
                }))

            # 3. 添加价格信息（拆分成多行）
            if prices := record.get('discountPrice'):
                # 对数量进行排序（1, 10, 100...）
                sorted_prices = sorted(
                    prices.items(),
                    key=lambda x: int(x[0]) if x[0].isdigit() else 0
                )

                for qty, price in sorted_prices:
                    line_ids.append((0, 0, {
                        'code': "discountPrice",
                        'key': qty,
                        'value': price,
                        'type': 'price',
                        'offsite_id': source_data.get('source_website_id')
                    }))
            # 获取库存信息
            if goodsNumber := record.get('goodsNumber'):
                website_inventory = goodsNumber

            if product_description := record.get('产品描述'):
                line_ids.append((0, 0, {
                    'code': "product_description",
                    'key': "描述",
                    'value': product_description,
                    'type': 'product_description',
                    'offsite_id': source_data.get('source_website_id')
                }))
            if highlight := record.get('highlight'):
                line_ids.append((0, 0, {
                    'code': "highlight",
                    'key': "参数",
                    'value': highlight,
                    'type': 'product_description',
                    'offsite_id': source_data.get('source_website_id')
                }))
        return {'line_ids': line_ids, 'website_inventory': website_inventory}

    def youhuo_product_price_inquiry(self):
        """
        有货商城数据获取，若cookie失效则自动重新登录（递归查找分类配置）
        :return:
        """
        offsite = self._get_category_website('youhuo_website_id', '有货商城')
        url = f"https://qjdz.com/getSearch?searchKey={self.name}"

        def _get_cookie_str():
            login_save_param = json.loads(offsite.login_save_param)
            # 兼容只存cookie字段的情况
            cookie_dict = login_save_param.get("cookie", login_save_param) if isinstance(login_save_param, dict) else {}
            return "; ".join([f"{k}={v}" for k, v in cookie_dict.items()])

        def _send_request():
            headers = {
                "User-Agent": "Mozilla/5.0",
                "Accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "Cookie": _get_cookie_str(),
            }
            response = requests.get(url, headers=headers, timeout=30)
            return response.text

        # 第一次请求
        html = _send_request()
        # 检查是否登录页
        if "欢迎登录" in html or "还没有账号" in html:
            # 自动重新登录
            offsite.login_website()
            # 重新获取cookie再请求一次
            html = _send_request()

        # 再检查一次，仍然是登录页就报错
        if "欢迎登录" in html or "还没有账号" in html:
            logging.info("有货商城cookie失效且自动登录失败，请检查登录账号和密码")

        return html

    def youhuo_product_detail_inquiry(self, stkc):
        """
        获取有货商城产品详情页面数据
        :param stkc: 产品编码
        :return: HTML内容
        """
        offsite = self._get_category_website('youhuo_website_id', '有货商城')
        url = f"https://qjdz.com/getDetail?stkc={stkc}"

        def _get_cookie_str():
            login_save_param = json.loads(offsite.login_save_param)
            # 兼容只存cookie字段的情况
            cookie_dict = login_save_param.get("cookie", login_save_param) if isinstance(login_save_param, dict) else {}
            return "; ".join([f"{k}={v}" for k, v in cookie_dict.items()])

        def _send_request():
            headers = {
                "User-Agent": "Mozilla/5.0",
                "Accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "Cookie": _get_cookie_str(),
            }
            response = requests.get(url, headers=headers, timeout=30)
            return response.text

        # 第一次请求
        html = _send_request()
        # 检查是否登录页
        if "欢迎登录" in html or "还没有账号" in html:
            # 自动重新登录
            offsite.login_website()
            # 重新获取cookie再请求一次
            html = _send_request()

        # 再检查一次，仍然是登录页就报错
        if "欢迎登录" in html or "还没有账号" in html:
            logging.info("有货商城cookie失效且自动登录失败，请检查登录账号和密码")

        return html

    def parse_youhuo_html(self):
        # 首先获取搜索页面
        search_html = self.youhuo_product_price_inquiry()
        search_soup = BeautifulSoup(search_html, 'html.parser')
        stock = None
        prices = []
        additional_info = []

        # 1. 从搜索页面提取库存（现货可供/PCS）
        # 找表头（th），确定是第几列
        stock_col_idx = None
        for th in search_soup.select('table.table tr th'):
            if '现货可供' in th.get_text():
                parent_tr = th.parent
                for idx, t in enumerate(parent_tr.find_all(['th', 'td'])):
                    if '现货可供' in t.get_text():
                        stock_col_idx = idx
                        break
                break

        # 拿到库存
        if stock_col_idx is not None:
            # 找到所有数据行
            for tr in search_soup.select('table.table tr'):
                tds = tr.find_all('td')
                if len(tds) > stock_col_idx:
                    stock_text = tds[stock_col_idx].get_text(strip=True)
                    if stock_text.isdigit():
                        stock = int(stock_text)
                        break

        # 2. 从搜索页面提取阶梯价格
        ul = search_soup.find('ul', class_='list-unstyled')
        if ul:
            for li in ul.find_all('li'):
                spans = li.find_all('span')
                if len(spans) >= 2:
                    qty = spans[0].get_text(strip=True).replace('+', '').replace(':', '').replace('：', '')
                    price = spans[1].get_text(strip=True).replace('￥', '').strip()
                    # 只保留数字价格
                    if price and price not in ['议价']:
                        youhuo_website = self._get_category_website('youhuo_website_id', '有货商城')
                        prices.append((0, 0, {'key': qty, 'value': price, 'type': 'price', 'offsite_id': youhuo_website.id}))

        # 3. 查找产品详情页面链接并提取产地和年份批次信息
        # 在搜索结果中查找产品链接
        stkc_code = None
        for link in search_soup.find_all('a', href=True):
            href = link['href']
            if 'getDetail?stkc=' in href:
                # 提取stkc参数
                import re
                match = re.search(r'stkc=([^&]+)', href)
                if match:
                    stkc_code = match.group(1)
                    break

        # 如果找到了产品编码，获取详情页面
        if stkc_code:
            try:
                detail_html = self.youhuo_product_detail_inquiry(stkc_code)
                detail_soup = BeautifulSoup(detail_html, 'html.parser')

                # 用于存储已找到的信息，避免重复
                found_info = {
                    'origin': set(),
                    'package': set(),
                    'description': set(),
                    'lots': set()
                }

                # 专门查找年份批次信息 - 优先级最高
                import re

                # 1. 首先尝试精确匹配"年份批次: xxx"格式
                page_text = detail_soup.get_text()
                batch_pattern = re.compile(r'年份批次[：:]\s*(\d+)')
                batch_match = batch_pattern.search(page_text)
                if batch_match:
                    batch_value = batch_match.group(1)
                    found_info['lots'].add(batch_value)
                    youhuo_website = self._get_category_website('youhuo_website_id', '有货商城')
                    additional_info.append((0, 0, {
                        'key': '年份批次',
                        'value': batch_value,
                        'type': 'lots',
                        'offsite_id': youhuo_website.id
                    }))

                # 从详情页面提取产地和其他信息
                for tr in detail_soup.select('table tr'):
                    tds = tr.find_all('td')
                    for i, td in enumerate(tds):
                        text = td.get_text(strip=True)

                        # 查找产地信息
                        if '产地' in text or '原产地' in text:
                            if i + 1 < len(tds):
                                origin_value = tds[i + 1].get_text(strip=True)
                                if origin_value and origin_value not in found_info['origin']:
                                    found_info['origin'].add(origin_value)
                                    youhuo_website = self._get_category_website('youhuo_website_id', '有货商城')
                                    additional_info.append((0, 0, {
                                        'key': '产地',
                                        'value': origin_value,
                                        'type': 'origin',
                                        'offsite_id': youhuo_website.id
                                    }))

                        # 查找包装相关信息
                        package_keywords = {
                            '最低订货量': 'package',
                            '最小包装量': 'package',
                            '整箱包装量': 'package',
                            '最小包装重量': 'package',
                            '整箱包装重量': 'package',
                            '包装大小': 'package'
                        }

                        for keyword, info_type in package_keywords.items():
                            if keyword in text:
                                if i + 1 < len(tds):
                                    value = tds[i + 1].get_text(strip=True)
                                    if value:
                                        key_value = f"{keyword}:{value}"
                                        if key_value not in found_info['package']:
                                            found_info['package'].add(key_value)
                                            youhuo_website = self._get_category_website('youhuo_website_id', '有货商城')
                                            additional_info.append((0, 0, {
                                                'key': keyword,
                                                'value': value,
                                                'type': info_type,
                                                'offsite_id': youhuo_website.id
                                            }))

                        # 查找其他产品描述信息
                        description_keywords = {
                            '品名': 'product_description',
                            '交期': 'other',
                            'batch': 'lots'
                        }

                        for keyword, info_type in description_keywords.items():
                            if keyword in text:
                                if i + 1 < len(tds):
                                    value = tds[i + 1].get_text(strip=True)
                                    if value:
                                        key_value = f"{keyword}:{value}"
                                        if key_value not in found_info['description']:
                                            found_info['description'].add(key_value)
                                            youhuo_website = self._get_category_website('youhuo_website_id', '有货商城')
                                            additional_info.append((0, 0, {
                                                'key': keyword,
                                                'value': value,
                                                'type': info_type,
                                                'offsite_id': youhuo_website.id
                                            }))

                        # 只有在没有找到精确年份批次格式时，才从表格中查找年份批次信息
                        if not found_info['lots'] and any(keyword in text for keyword in ['年份', '批次', 'DC', '日期码', '生产日期', '制造日期']):
                            if i + 1 < len(tds):
                                batch_value = tds[i + 1].get_text(strip=True)
                                if batch_value:
                                    found_info['lots'].add(batch_value)
                                    youhuo_website = self._get_category_website('youhuo_website_id', '有货商城')
                                    additional_info.append((0, 0, {
                                        'key': '年份批次',
                                        'value': batch_value,
                                        'type': 'lots',
                                        'offsite_id': youhuo_website.id
                                    }))

                # 如果前面的精确匹配和表格解析都没有找到年份批次，尝试其他格式
                if not found_info['lots']:
                    # 更严格的模式匹配，避免误匹配
                    strict_patterns = [
                        r'(?:DC|dc)\s*(20\d{2})',  # 匹配DC2024、DC 2024等
                        r'(?:年份|批次|生产日期|制造日期)[：:\s]*(20\d{4})',  # 匹配年份:202503等
                        r'(?:年份|批次|生产日期|制造日期)[：:\s]*(20\d{2})',   # 匹配年份:2024等
                    ]

                    for element in detail_soup.find_all(['div', 'span', 'p']):
                        text = element.get_text(strip=True)

                        for pattern in strict_patterns:
                            matches = re.findall(pattern, text, re.IGNORECASE)
                            for match in matches:
                                if match not in found_info['lots']:
                                    found_info['lots'].add(match)
                                    youhuo_website = self._get_category_website('youhuo_website_id', '有货商城')
                                    additional_info.append((0, 0, {
                                        'key': '年份批次',
                                        'value': match,
                                        'type': 'lots',
                                        'offsite_id': youhuo_website.id
                                    }))
                                    break  # 找到一个就够了

                        if found_info['lots']:  # 如果已经找到了，就不再继续
                            break
            except Exception as e:
                logging.warning(f"获取有货商城产品详情失败: {e}")

        return {
            "stock": stock,
            "prices": prices,
            "additional_info": additional_info
        }

    def get_jlc_product_data(self):
        offsite = self._get_category_website('jlc_website_id', 'JLC')
        # 如果 login_param 是 JSON 字符串，需要先转换为字典
        if isinstance(offsite.login_param, str):
            user_info = json.loads(offsite.login_param)
        else:
            user_info = dict(offsite.login_param)
        username = user_info.get('username')
        password = user_info.get('password')

        ali = Ali(debug=False)
        ali_item = ali.run()
        if not ali_item:
            return {'line_ids': [], 'website_inventory': 0.0}

        jtc = Jtc(username=username,password=password,debug=False)
        item_list = jtc.run(ali_item, self.name)
        if not item_list:
            return {'line_ids': [], 'website_inventory': 0.0}
        
        
        
        records = [item for item in item_list if item["产品型号"] == self.name]

        website_inventory = 0.0
        line_ids = []

        # 处理每个返回的记录
        for record in records:
            # 1. 添加包装信息
            if package_type := record.get('type'):
                line_ids.append((0, 0, {
                    'code': 'package',
                    'key': '包装',
                    'value': package_type,
                    'type': 'package',
                    'offsite_id': offsite.id
                }))

            # 2. 添加价格信息（阶梯价格）
            if price_list := record.get('价格列表'):
                for price_item in price_list:
                    for qty, price in price_item.items():
                        # 去掉价格中的￥符号和数量中的+号
                        clean_price = price.replace('￥', '').strip()
                        clean_qty = qty.replace('+', '').strip()
                        line_ids.append((0, 0, {
                            'code': "price_list",
                            'key': clean_qty,
                            'value': clean_price,
                            'type': 'price',
                            'offsite_id': offsite.id
                        }))

            # 3. 添加库存信息（动态处理所有仓库）
            total_stock = 0
            warehouse_keys = [key for key in record.keys() if key.endswith('仓')]
            
            for warehouse_key in warehouse_keys:
                stock_value = record.get(warehouse_key, 0)
                if stock_value is None:
                    stock_value = 0
                total_stock += stock_value
                
                # 为每个仓库创建一个库存记录
                line_ids.append((0, 0, {
                    'code': f'{warehouse_key}_stock',
                    'key': warehouse_key,
                    'value': str(stock_value),
                    'type': 'product_description',
                    'offsite_id': offsite.id
                }))
            
            website_inventory = max(website_inventory, total_stock)

        return {'line_ids': line_ids, 'website_inventory': website_inventory}

    def get_yunhan_product_data(self):
        """ 云汉芯城数据获取（递归查找分类配置） """
        offsite = self._get_category_website('yunhan_website_id', '云汉芯城')
        # 如果 login_param 是 JSON 字符串，需要先转换为字典
        if isinstance(offsite.login_param, str):
            user_info = json.loads(offsite.login_param)
        else:
            user_info = dict(offsite.login_param)
        username = user_info.get('username')
        password = user_info.get('password')
        token = user_info.get('token')

        code = WordPage(token)
        check_item = code.run()
        ick = ICKey(username, password)
        ick.login(check_item)
        products = ick.search(self.name)
        
        # 处理返回的数据
        website_inventory = 0.0
        line_ids = []
        
        # 处理每个返回的记录
        for record in products:
            # 1. 添加价格梯度信息
            if price_gradient := record.get('价格梯度'):
                for price_item in price_gradient:
                    for qty, price in price_item.items():
                        # 清理数量字符串（去掉+号）
                        clean_qty = qty.replace('+', '').strip()
                        line_ids.append((0, 0, {
                            'code': "price_gradient",
                            'key': clean_qty,
                            'value': str(price),
                            'type': 'price',
                            'offsite_id': offsite.id
                        }))

            # 2. 获取库存数量
            if stock_quantity := record.get('库存数量'):
                website_inventory = max(website_inventory, stock_quantity)

        return {'line_ids': line_ids, 'website_inventory': website_inventory}

    def action_price_inquiry_action(self):
        """
        打开站外价格查询向导窗口
        """
        # 京北商城
        try:
            jbchip_data = self.jbchip_product_price_inquiry()
        except Exception:
            jbchip_data = {'website_inventory': 0, 'line_ids': []}

        # 有货商城
        try:
            youhuo_data = self.parse_youhuo_html()
        except Exception:
            youhuo_data = {'stock': 0, 'prices': [], 'additional_info': []}

        # JLC数据获取
        try:
            jlc_data = self.get_jlc_product_data()
        except Exception:
            jlc_data = {'website_inventory': 0, 'line_ids': []}

        # 云汉芯城数据获取
        try:
            yunhan_data = self.get_yunhan_product_data()
        except Exception as e:
            yunhan_data = {'website_inventory': 0, 'line_ids': []}

        # 打开向导窗口
        return {
            'type': 'ir.actions.act_window',
            'name': '站外价格查询',
            'res_model': 'offsite.price.inquiry.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_product_id': self.id,
                'default_website_inventory': jbchip_data['website_inventory'],
                'default_line_ids': jbchip_data['line_ids'],
                'default_youhuo_inventory': youhuo_data['stock'],
                'default_youhuo_ids': youhuo_data['prices'] + youhuo_data.get('additional_info', []),
                'default_jlc_inventory': jlc_data['website_inventory'],
                'default_jlc_ids': jlc_data['line_ids'],
                'default_yunhan_inventory': yunhan_data['website_inventory'],
                'default_yunhan_ids': yunhan_data['line_ids'],
            }
        }
