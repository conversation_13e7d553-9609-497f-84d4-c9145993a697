<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 扩展采购订单表单视图，添加价格表相关按钮 -->
    <record id="purchase_order_form_inherit" model="ir.ui.view">
        <field name="name">purchase.order.form.inherit</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header/button[@name='action_rfq_send']" position="after">
                <button name="action_generate_supplierinfo" 
                        string="更新到供应商价格表"
                        icon="fa-arrow-up" 
                        type="object" 
                        class="btn-success" 
                        invisible="state not in ['draft', 'sent', 'purchase']"/>
                <button name="action_update_order_line" 
                        string="应用价格"
                        icon="fa-arrow-down" 
                        type="object" 
                        class="btn-info" 
                        invisible="state not in ['draft', 'sent', 'purchase']"/>
                <button name="copy_purchase_line_info" 
                        string="复制产品行信息"
                        icon="fa-copy" 
                        type="object" 
                        class="btn-warning" />
            </xpath>

            <xpath expr="//field[@name='order_line']/list/field[@name='product_id']" position="after">
                    <button name="copy_product_template_name"
                            type="object"
                            icon="fa-copy"
                            class="btn btn-sm o_button_icon p-1"
                            title="复制产品模板名称"/>
            </xpath>
            
            <!-- 添加采购订单行中的同步按钮和相关字段 -->
            <xpath expr="//field[@name='order_line']/list" position="attributes">
                <attribute name="decoration-info">show_price_sync_btn == False</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list/field[@name='price_unit']" position="after">
                <field name="supplier_pricelist_count" column_invisible="1"/>
                <field name="show_price_sync_btn" column_invisible="1"/>
                <button name="action_sync_price_to_pricelist"
                        type="object"
                        icon="fa-refresh"
                        title="同步价格到供应商价格表"
                        class="btn btn-sm o_button_icon p-1"
                        invisible="0"
                        context="{'skip_save': 1, 'skip_save_fields': ['price_unit', 'order_id', 'product_id', 'product_qty']}" />
            </xpath>
        </field>
    </record>
</odoo> 