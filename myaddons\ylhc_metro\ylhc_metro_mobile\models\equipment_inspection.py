# -*- coding: utf-8 -*-
from datetime import datetime

from odoo import models, fields, api

class EquipmentInspection(models.Model):
    _name = "equipment.inspection"
    _description = "设备巡检"
    _rec_name = "name"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'inspection_date desc, id desc'

    name = fields.Char('编号', default=lambda self: self._generate_code(), tracking=True)
    line_id = fields.Many2one('metro.line', '巡检线路', tracking=True, index=True)
    location_id = fields.Many2one('metro.station', '巡检站点', tracking=True, index=True)
    start_time = fields.Datetime('巡检开始时间', tracking=True, index=True)
    end_time = fields.Datetime('巡检结束时间', tracking=True)
    inspection_date = fields.Date('巡检日期',default=fields.Date.today(), tracking=True, index=True)
    type_id = fields.Many2one('inspection.type', '巡检类型', tracking=True)
    major_id = fields.Many2one('inspection.major', '巡检专业', tracking=True, required=True, index=True)
    inspection_person_ids = fields.Many2many('hr.employee', string='巡检人员', tracking=True)
    inspection_system_ids = fields.One2many('equipment.inspection.system.line', 'inspection_id', string='巡检系统')
    note = fields.Text('备注')
    
    # 添加状态字段
    state = fields.Selection([
        ('draft', '草稿'),
        ('in_progress', '进行中'),
        ('done', '已完成'),
        ('cancel', '已取消')
    ], string='状态', default='draft', tracking=True)
    
    # 添加统计字段
    total_items = fields.Integer('总项目数', compute='_compute_statistics', store=True)
    completed_items = fields.Integer('已完成项目数', compute='_compute_statistics', store=True)
    abnormal_items = fields.Integer('异常项目数', compute='_compute_statistics', store=True)
    completion_rate = fields.Float('完成率(%)', compute='_compute_statistics', store=True)

    
    @api.depends('inspection_system_ids.subsystem_ids.result')
    def _compute_statistics(self):
        """计算巡检统计数据"""
        for record in self:
            subsystem_lines = record.inspection_system_ids.mapped('subsystem_ids')
            total = len(subsystem_lines)
            completed = len(subsystem_lines.filtered(lambda x: x.result))
            abnormal = len(subsystem_lines.filtered(lambda x: x.result == 'abnormal'))
            
            record.total_items = total
            record.completed_items = completed
            record.abnormal_items = abnormal
            record.completion_rate = (completed / total * 100) if total > 0 else 0
    
    @api.onchange('major_id')
    def _onchange_major_id(self):
        """当选择巡检专业时，自动创建对应的系统和子系统行"""
        if self.major_id:
            # 清空现有的系统行
            self.inspection_system_ids = [(5, 0, 0)]
            
            # 获取该专业包含的所有系统
            systems = self.major_id.system_ids
            system_lines = []
            
            for system in systems:
                # 为每个系统创建系统行
                subsystem_lines = []
                for subsystem in system.inspection_system_line_ids:
                    subsystem_lines.append((0, 0, {
                        'subsystem_id': subsystem.id,
                        'result': False,  # 默认未检查
                    }))
                
                system_lines.append((0, 0, {
                    'system_id': system.id,
                    'subsystem_ids': subsystem_lines,
                }))
            
            self.inspection_system_ids = system_lines
    
    def action_start_inspection(self):
        """开始巡检"""
        self.write({
            'state': 'in_progress',
            'start_time': fields.Datetime.now()
        })
        return True
    
    def action_complete_inspection(self):
        """完成巡检"""
        self.write({
            'state': 'done',
            'end_time': fields.Datetime.now()
        })
        return True
    
    def action_cancel_inspection(self):
        """取消巡检"""
        self.write({'state': 'cancel'})
        return True
    
    def action_view_attachments(self):
        """查看所有附件"""
        attachment_ids = self.inspection_system_ids.mapped('subsystem_ids.attachment_ids').ids
        return {
            'type': 'ir.actions.act_window',
            'name': '巡检附件',
            'res_model': 'ir.attachment',
            'view_mode': 'kanban,list,form',
            'domain': [('id', 'in', attachment_ids)],
            'context': self.env.context,
        }
    
    def action_view_completed(self):
        """查看已完成项目"""
        subsystem_ids = self.inspection_system_ids.mapped('subsystem_ids').filtered(lambda x: x.result).ids
        return {
            'type': 'ir.actions.act_window',
            'name': '已完成项目',
            'res_model': 'equipment.inspection.subsystem.line',
            'view_mode': 'list,form',
            'domain': [('id', 'in', subsystem_ids)],
            'context': self.env.context,
        }
    
    def action_view_abnormal(self):
        """查看异常项目"""
        subsystem_ids = self.inspection_system_ids.mapped('subsystem_ids').filtered(lambda x: x.result == 'abnormal').ids
        return {
            'type': 'ir.actions.act_window',
            'name': '异常项目',
            'res_model': 'equipment.inspection.subsystem.line',
            'view_mode': 'list,form',
            'domain': [('id', 'in', subsystem_ids)],
            'context': self.env.context,
        }


    @api.model
    def _generate_code(self):
        # 获取当前日期
        date_str = datetime.today().strftime('%Y%m%d')

        # 查找当天已创建的记录数量
        last_record = self.search([('name', 'like', f'TCMWB-SBXJ-{date_str}%')], order='name desc', limit=1)

        if last_record and last_record.name:
            # 获取最后一个编号的流水号并递增
            last_number = int(last_record.name[-3:]) + 1
        else:
            # 如果是当天第一条记录，设置初始流水号
            last_number = 1

        # 组装新的编号
        new_code = f'TCMWB-SBXJ-{date_str}{str(last_number).zfill(3)}'
        return new_code

    # ==================== 数据看板相关方法 ====================

    @api.model
    def get_dashboard_data(self, month=None, line_id=None, major_id=None):
        """获取设备巡检看板数据"""
        try:
            import logging
            _logger = logging.getLogger(__name__)

            # 先检查是否有数据，如果没有数据返回模拟数据
            total_records = self.search_count([])
            _logger.info(f"数据库中总共有 {total_records} 条设备巡检记录")

            if total_records == 0:
                # 返回模拟数据用于测试
                _logger.info("数据库中没有设备巡检记录，返回模拟数据")
                return {
                    'total_count': 45,
                    'completed_count': 38,
                    'abnormal_count': 7,
                    'has_note_count': 12,
                    'major_stats': [
                        {'name': '机电专业', 'value': 25},
                        {'name': '通信专业', 'value': 12},
                        {'name': '信号专业', 'value': 8}
                    ],
                    'system_stats': [
                        {'name': '空调通风系统', 'value': 15},
                        {'name': '给排水系统', 'value': 12},
                        {'name': '电梯扶梯系统', 'value': 10},
                        {'name': '照明系统', 'value': 8}
                    ],
                    'station_stats': [
                        {'name': '1号线-天府广场', 'value': 8},
                        {'name': '2号线-春熙路', 'value': 6},
                        {'name': '3号线-锦里', 'value': 5},
                        {'name': '4号线-宽窄巷子', 'value': 4}
                    ],
                    'station_issue_stats': [
                        {'name': '1号线-天府广场', 'value': 3},
                        {'name': '2号线-春熙路', 'value': 2},
                        {'name': '3号线-锦里', 'value': 1},
                        {'name': '4号线-宽窄巷子', 'value': 1}
                    ]
                }

            # 如果有数据，执行实际查询
            domain = []
            if month:
                domain.append(('inspection_date', '>=', f'{month}-01'))
                # 获取月份的最后一天
                import calendar
                year, month_num = map(int, month.split('-'))
                last_day = calendar.monthrange(year, month_num)[1]
                domain.append(('inspection_date', '<=', f'{month}-{last_day:02d}'))

            if line_id:
                domain.append(('line_id', '=', int(line_id)))

            if major_id:
                domain.append(('major_id', '=', int(major_id)))

            # 基础统计
            total_count = self.search_count(domain)
            completed_domain = domain + [('state', '=', 'done')]
            completed_count = self.search_count(completed_domain)

            # 异常统计 - 通过子系统统计
            abnormal_count = 0
            has_note_count = 0

            inspections = self.search(domain)
            for inspection in inspections:
                # 统计异常项目
                abnormal_items = inspection.inspection_system_ids.mapped('subsystem_ids').filtered(lambda x: x.result == 'abnormal')
                if abnormal_items:
                    abnormal_count += 1

                # 统计有备注的记录
                if inspection.note or any(sub.note for sub in inspection.inspection_system_ids.mapped('subsystem_ids')):
                    has_note_count += 1

            # 获取各类统计数据 - 暂时使用简化版本
            major_stats = []
            system_stats = []
            station_stats = []
            station_issue_stats = []

            return {
                'total_count': total_count,
                'completed_count': completed_count,
                'abnormal_count': abnormal_count,
                'has_note_count': has_note_count,
                'major_stats': major_stats,
                'system_stats': system_stats,
                'station_stats': station_stats,
                'station_issue_stats': station_issue_stats
            }

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"获取设备巡检看板数据失败: {str(e)}")
            return {
                'total_count': 0,
                'completed_count': 0,
                'abnormal_count': 0,
                'has_note_count': 0,
                'major_stats': [],
                'system_stats': [],
                'station_stats': [],
                'station_issue_stats': []
            }

    @api.model
    def get_major_stats(self, month=None, line_id=None):
        """获取巡检专业统计数据"""
        try:
            domain = []
            if month:
                domain.append(('inspection_date', '>=', f'{month}-01'))
                from datetime import datetime
                import calendar
                year, month_num = map(int, month.split('-'))
                last_day = calendar.monthrange(year, month_num)[1]
                domain.append(('inspection_date', '<=', f'{month}-{last_day:02d}'))

            if line_id:
                domain.append(('line_id', '=', int(line_id)))

            # 按专业分组统计
            query = """
                SELECT
                    im.name as major_name,
                    COUNT(ei.id) as count
                FROM equipment_inspection ei
                LEFT JOIN inspection_major im ON ei.major_id = im.id
                WHERE 1=1
            """
            params = []

            if month:
                query += " AND ei.inspection_date >= %s AND ei.inspection_date <= %s"
                year, month_num = map(int, month.split('-'))
                import calendar
                last_day = calendar.monthrange(year, month_num)[1]
                params.extend([f'{month}-01', f'{month}-{last_day:02d}'])

            if line_id:
                query += " AND ei.line_id = %s"
                params.append(int(line_id))

            query += " GROUP BY im.id, im.name ORDER BY count DESC LIMIT 10"

            self.env.cr.execute(query, params)
            results = self.env.cr.fetchall()

            return [{'name': name, 'value': count} for name, count in results]

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"获取专业统计失败: {str(e)}")
            return []

    @api.model
    def get_system_stats(self, month=None, line_id=None, major_id=None):
        """获取系统统计数据"""
        try:
            domain = []
            if month:
                domain.append(('inspection_date', '>=', f'{month}-01'))
                import calendar
                year, month_num = map(int, month.split('-'))
                last_day = calendar.monthrange(year, month_num)[1]
                domain.append(('inspection_date', '<=', f'{month}-{last_day:02d}'))

            if line_id:
                domain.append(('line_id', '=', int(line_id)))

            if major_id:
                domain.append(('major_id', '=', int(major_id)))

            # 按系统分组统计
            query = """
                SELECT
                    ins.name as system_name,
                    COUNT(eisl.id) as count
                FROM equipment_inspection_system_line eisl
                LEFT JOIN equipment_inspection ei ON eisl.inspection_id = ei.id
                LEFT JOIN inspection_system ins ON eisl.system_id = ins.id
                WHERE 1=1
            """
            params = []

            if month:
                query += " AND ei.inspection_date >= %s AND ei.inspection_date <= %s"
                year, month_num = map(int, month.split('-'))
                import calendar
                last_day = calendar.monthrange(year, month_num)[1]
                params.extend([f'{month}-01', f'{month}-{last_day:02d}'])

            if line_id:
                query += " AND ei.line_id = %s"
                params.append(int(line_id))

            if major_id:
                query += " AND ei.major_id = %s"
                params.append(int(major_id))

            query += " GROUP BY ins.id, ins.name ORDER BY count DESC LIMIT 10"

            self.env.cr.execute(query, params)
            results = self.env.cr.fetchall()

            return [{'name': name, 'value': count} for name, count in results]

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"获取系统统计失败: {str(e)}")
            return []

    @api.model
    def get_station_stats(self, month=None, line_id=None, major_id=None):
        """获取站点统计数据 - 站点维度，展示每个站点的巡检次数"""
        try:
            domain = []
            if month:
                domain.append(('inspection_date', '>=', f'{month}-01'))
                import calendar
                year, month_num = map(int, month.split('-'))
                last_day = calendar.monthrange(year, month_num)[1]
                domain.append(('inspection_date', '<=', f'{month}-{last_day:02d}'))

            if line_id:
                domain.append(('line_id', '=', int(line_id)))

            if major_id:
                domain.append(('major_id', '=', int(major_id)))

            # 按站点分组统计 - 使用PostgreSQL的CONCAT函数或者||操作符
            query = """
                SELECT
                    COALESCE(ml.name || '-' || ms.name, '未知站点') as station_name,
                    COUNT(ei.id) as count
                FROM equipment_inspection ei
                LEFT JOIN metro_station ms ON ei.location_id = ms.id
                LEFT JOIN metro_line ml ON ei.line_id = ml.id
                WHERE 1=1
            """
            params = []

            if month:
                query += " AND ei.inspection_date >= %s AND ei.inspection_date <= %s"
                year, month_num = map(int, month.split('-'))
                import calendar
                last_day = calendar.monthrange(year, month_num)[1]
                params.extend([f'{month}-01', f'{month}-{last_day:02d}'])

            if line_id:
                query += " AND ei.line_id = %s"
                params.append(int(line_id))

            if major_id:
                query += " AND ei.major_id = %s"
                params.append(int(major_id))

            query += " GROUP BY ml.name, ms.name ORDER BY count DESC LIMIT 10"

            self.env.cr.execute(query, params)
            results = self.env.cr.fetchall()

            return [{'name': name or '未知站点', 'value': count} for name, count in results]

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"获取站点统计失败: {str(e)}")
            return []

    @api.model
    def get_station_issue_stats(self, month=None, line_id=None, major_id=None):
        """获取站点问题统计数据 - 各站点的异常次数统计"""
        try:
            # 按站点统计异常次数
            query = """
                SELECT
                    COALESCE(ml.name || '-' || ms.name, '未知站点') as station_name,
                    COUNT(DISTINCT ei.id) as count
                FROM equipment_inspection ei
                LEFT JOIN metro_station ms ON ei.location_id = ms.id
                LEFT JOIN metro_line ml ON ei.line_id = ml.id
                LEFT JOIN equipment_inspection_system_line eisl ON eisl.inspection_id = ei.id
                LEFT JOIN equipment_inspection_subsystem_line eissl ON eissl.inspection_system_line_id = eisl.id
                WHERE eissl.result = 'abnormal'
            """
            params = []

            if month:
                query += " AND ei.inspection_date >= %s AND ei.inspection_date <= %s"
                year, month_num = map(int, month.split('-'))
                import calendar
                last_day = calendar.monthrange(year, month_num)[1]
                params.extend([f'{month}-01', f'{month}-{last_day:02d}'])

            if line_id:
                query += " AND ei.line_id = %s"
                params.append(int(line_id))

            if major_id:
                query += " AND ei.major_id = %s"
                params.append(int(major_id))

            query += " GROUP BY ml.name, ms.name ORDER BY count DESC LIMIT 10"

            self.env.cr.execute(query, params)
            results = self.env.cr.fetchall()

            return [{'name': name or '未知站点', 'value': count} for name, count in results]

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"获取站点异常统计失败: {str(e)}")
            return []

    @api.model
    def get_inspection_trends(self, days=30, line_id=None, major_id=None, view_mode='daily', month=None):
        """获取巡检趋势数据"""
        try:
            from datetime import datetime, timedelta
            import logging
            _logger = logging.getLogger(__name__)

            _logger.info(f"开始获取设备巡检趋势数据: days={days}, line_id={line_id}, major_id={major_id}, view_mode={view_mode}, month={month}")

            domain = []
            if month:
                domain.append(('inspection_date', '>=', f'{month}-01'))
                import calendar
                year, month_num = map(int, month.split('-'))
                last_day = calendar.monthrange(year, month_num)[1]
                domain.append(('inspection_date', '<=', f'{month}-{last_day:02d}'))
            else:
                # 如果没有指定月份，使用天数范围
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=days-1)
                domain.extend([
                    ('inspection_date', '>=', start_date),
                    ('inspection_date', '<=', end_date)
                ])

            if line_id:
                domain.append(('line_id', '=', int(line_id)))

            if major_id:
                domain.append(('major_id', '=', int(major_id)))

            if view_mode == 'weekly':
                return self._get_weekly_trends(domain, month)
            else:
                return self._get_daily_trends(domain, days, month)

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"获取设备巡检趋势数据失败: {str(e)}")
            return {
                'dates': [],
                'total': [],
                'completed': [],
                'abnormal': []
            }

    def _get_daily_trends(self, domain, days, month=None):
        """获取每日趋势数据"""
        from datetime import datetime, timedelta

        if month:
            # 如果指定了月份，获取该月的所有天数
            year, month_num = map(int, month.split('-'))
            import calendar
            last_day = calendar.monthrange(year, month_num)[1]
            start_date = datetime.strptime(f'{month}-01', '%Y-%m-%d').date()
            end_date = datetime.strptime(f'{month}-{last_day:02d}', '%Y-%m-%d').date()
            days = (end_date - start_date).days + 1
        else:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days-1)

        # 生成日期列表
        dates = []
        total = []
        completed = []
        abnormal = []

        for i in range(days):
            current_date = start_date + timedelta(days=i)
            dates.append(current_date.strftime('%m-%d'))

            # 查询当天的数据
            day_domain = domain + [('inspection_date', '=', current_date)]
            inspections = self.search(day_domain)

            total_count = len(inspections)
            completed_count = len(inspections.filtered(lambda x: x.state == 'done'))

            # 统计异常数量
            abnormal_count = 0
            for inspection in inspections:
                abnormal_items = inspection.inspection_system_ids.mapped('subsystem_ids').filtered(lambda x: x.result == 'abnormal')
                if abnormal_items:
                    abnormal_count += 1

            total.append(total_count)
            completed.append(completed_count)
            abnormal.append(abnormal_count)

        return {
            'dates': dates,
            'total': total,
            'completed': completed,
            'abnormal': abnormal
        }

    def _get_weekly_trends(self, domain, month=None):
        """获取每周趋势数据"""
        from datetime import datetime, timedelta
        import calendar

        if month:
            year, month_num = map(int, month.split('-'))
            # 获取该月的周数据
            first_day = datetime(year, month_num, 1).date()
            last_day = datetime(year, month_num, calendar.monthrange(year, month_num)[1]).date()
        else:
            # 获取最近4周的数据
            end_date = datetime.now().date()
            start_date = end_date - timedelta(weeks=4)
            first_day = start_date
            last_day = end_date

        # 计算周数据
        weeks = []
        total = []
        completed = []
        abnormal = []

        current_date = first_day
        week_num = 1

        while current_date <= last_day:
            week_end = min(current_date + timedelta(days=6), last_day)
            week_label = f'第{week_num}周'
            weeks.append(week_label)

            # 查询该周的数据
            week_domain = domain + [
                ('inspection_date', '>=', current_date),
                ('inspection_date', '<=', week_end)
            ]
            inspections = self.search(week_domain)

            total_count = len(inspections)
            completed_count = len(inspections.filtered(lambda x: x.state == 'done'))

            # 统计异常数量
            abnormal_count = 0
            for inspection in inspections:
                abnormal_items = inspection.inspection_system_ids.mapped('subsystem_ids').filtered(lambda x: x.result == 'abnormal')
                if abnormal_items:
                    abnormal_count += 1

            total.append(total_count)
            completed.append(completed_count)
            abnormal.append(abnormal_count)

            current_date = week_end + timedelta(days=1)
            week_num += 1

        return {
            'dates': weeks,
            'total': total,
            'completed': completed,
            'abnormal': abnormal
        }

    @api.model
    def get_inspection_efficiency_stats(self, month=None, line_id=None, major_id=None):
        """获取巡检效率统计"""
        try:
            domain = []
            if month:
                domain.append(('inspection_date', '>=', f'{month}-01'))
                import calendar
                year, month_num = map(int, month.split('-'))
                last_day = calendar.monthrange(year, month_num)[1]
                domain.append(('inspection_date', '<=', f'{month}-{last_day:02d}'))

            if line_id:
                domain.append(('line_id', '=', int(line_id)))

            if major_id:
                domain.append(('major_id', '=', int(major_id)))

            inspections = self.search(domain)
            total_count = len(inspections)

            if total_count == 0:
                return {
                    'completion_rate': 0,
                    'abnormal_rate': 0,
                    'note_rate': 0,
                    'avg_duration': 0,
                    'total_count': 0
                }

            # 完成率
            completed_count = len(inspections.filtered(lambda x: x.state == 'done'))
            completion_rate = (completed_count / total_count * 100) if total_count > 0 else 0

            # 异常率
            abnormal_count = 0
            note_count = 0
            total_duration = 0
            duration_count = 0

            for inspection in inspections:
                # 统计异常项目
                abnormal_items = inspection.inspection_system_ids.mapped('subsystem_ids').filtered(lambda x: x.result == 'abnormal')
                if abnormal_items:
                    abnormal_count += 1

                # 统计有备注的记录
                if inspection.note or any(sub.note for sub in inspection.inspection_system_ids.mapped('subsystem_ids')):
                    note_count += 1

                # 计算平均时长（分钟）
                if inspection.start_time and inspection.end_time:
                    duration = (inspection.end_time - inspection.start_time).total_seconds() / 60
                    total_duration += duration
                    duration_count += 1

            abnormal_rate = (abnormal_count / total_count * 100) if total_count > 0 else 0
            note_rate = (note_count / total_count * 100) if total_count > 0 else 0
            avg_duration = (total_duration / duration_count) if duration_count > 0 else 0

            return {
                'completion_rate': round(completion_rate, 1),
                'abnormal_rate': round(abnormal_rate, 1),
                'note_rate': round(note_rate, 1),
                'avg_duration': round(avg_duration, 1),
                'total_count': total_count
            }

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"获取设备巡检效率统计失败: {str(e)}")
            return {
                'completion_rate': 0,
                'abnormal_rate': 0,
                'note_rate': 0,
                'avg_duration': 0,
                'total_count': 0
            }




class EquipmentInspectionSystemLine(models.Model):
    _name = "equipment.inspection.system.line"
    _description = "设备巡检系统行"
    _rec_name = 'inspection_id'

    inspection_id = fields.Many2one('equipment.inspection', '设备巡检', ondelete='cascade', required=True, index=True)
    system_id = fields.Many2one('inspection.system', '巡检系统', required=True)
    subsystem_ids = fields.One2many('equipment.inspection.subsystem.line', 'inspection_system_line_id', string='巡检子系统')
    
    # 添加统计字段
    subsystem_count = fields.Integer('子系统总数', compute='_compute_subsystem_stats', store=True)
    completed_count = fields.Integer('已完成数', compute='_compute_subsystem_stats', store=True)
    abnormal_count = fields.Integer('异常数', compute='_compute_subsystem_stats', store=True)
    
    @api.depends('subsystem_ids.result')
    def _compute_subsystem_stats(self):
        """计算子系统统计数据"""
        for record in self:
            subsystems = record.subsystem_ids
            record.subsystem_count = len(subsystems)
            record.completed_count = len(subsystems.filtered(lambda x: x.result))
            record.abnormal_count = len(subsystems.filtered(lambda x: x.result == 'abnormal'))
    
    def action_view_subsystems(self):
        """查看子系统列表 - 直接跳转到子系统列表，可以点击进入表单查看图片"""
        return {
            'type': 'ir.actions.act_window',
            'name': f'{self.system_id.name} - 子系统检查',
            'res_model': 'equipment.inspection.subsystem.line',
            'view_mode': 'list,form',
            'path':"subsystem",
            'domain': [('inspection_system_line_id', '=', self.id)],
            'context': {
                'default_inspection_system_line_id': self.id,
            },
            'target': 'current',  # 在当前窗口打开
        }


class EquipmentInspectionSubsystemLine(models.Model):
    _name = 'equipment.inspection.subsystem.line'
    _description = '设备巡检子系统行'
    _rec_name = 'inspection_system_line_id'

    inspection_system_line_id = fields.Many2one('equipment.inspection.system.line', '设备巡检系统行', ondelete='cascade', required=True, index=True)
    subsystem_id = fields.Many2one('inspection.system.line', '巡检子系统', required=True)
    attachment_ids = fields.Many2many('ir.attachment', string='附件')
    result = fields.Selection([('normal', '正常'), ('abnormal', '异常')], string='巡检结果')
    note = fields.Text('备注')
    inspection_time = fields.Datetime('检查时间')
    inspector_ids = fields.Many2many('hr.employee', string='检查人员')
    
