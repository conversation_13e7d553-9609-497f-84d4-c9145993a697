// Vue3 + Element Plus 设备巡检看板应用
const { createApp, ref, onMounted, onUnmounted } = Vue;

let charts = {
    majorChart: null,
    systemChart: null,
    stationChart: null,
    issueChart: null,
    trendChart: null
};

let mainMajorChartOption = null;

// Vue应用实例变量
let vueInstance = null;

// Vue应用
const dashboardApp = createApp({
    setup() {
        // 响应式数据
        const currentDate = ref('');
        const currentTime = ref('');
        const selectedMonth = ref(null);
        const selectedLine = ref(null);
        const selectedMajor = ref(null);
        const lineOptions = ref([]);
        const majorOptions = ref([]);
        const currentDataScope = ref(null);
        const loading = ref(false);
        const showDrillBackBtn = ref(false);
        const trendViewMode = ref('daily');
        
        // 看板数据
        const dashboardData = ref({
            total_count: 0,
            completed_count: 0,
            abnormal_count: 0,
            has_note_count: 0,
            major_stats: [],
            system_stats: [],
            station_stats: [],
            station_issue_stats: []
        });

        let timeInterval = null;

        // 更新日期时间
        const updateDateTime = () => {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            
            currentDate.value = `${year}年${month}月${day}日`;
            currentTime.value = `${hours}:${minutes}:${seconds}`;
        };

        // 初始化图表
        const initCharts = () => {
            console.log('initCharts 函数被调用');
            
            // 等待DOM完全渲染
            setTimeout(() => {
                // 立即设置容器样式，防止尺寸计算错误
                const containers = ['majorChart', 'systemChart', 'stationChart', 'issueChart', 'trendChart'];
                containers.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        // 预设固定样式，防止动态计算导致的抖动
                        element.style.width = '100%';
                        element.style.height = '100%';
                        element.style.minWidth = '200px';
                        element.style.minHeight = '200px';
                        element.style.maxWidth = '100%';
                        element.style.boxSizing = 'border-box';
                        element.style.display = 'block';
                        element.style.position = 'relative';
                        element.style.transition = 'none';
                        element.style.background = 'transparent';
                        
                        console.log(`预设 ${id} 容器样式完成，尺寸: ${element.offsetWidth}x${element.offsetHeight}`);
                    } else {
                        console.error(`容器 ${id} 未找到`);
                    }
                });
                
                // 使用 requestAnimationFrame 确保样式应用后再初始化图表
                requestAnimationFrame(() => {
                    console.log('开始初始化各个图表实例...');
                    
                    // 巡检专业分布饼图
                    const majorElement = document.getElementById('majorChart');
                    if (majorElement && !charts.majorChart) {
                        try {
                            charts.majorChart = echarts.init(majorElement, null, {
                                renderer: 'canvas',
                                useDirtyRect: false,
                                width: majorElement.offsetWidth || 300,
                                height: majorElement.offsetHeight || 250
                            });
                            console.log('majorChart 初始化完成');
                        } catch (e) {
                            console.error('majorChart 初始化失败:', e);
                        }
                    }
                    
                    // 系统分布柱状图
                    const systemElement = document.getElementById('systemChart');
                    if (systemElement && !charts.systemChart) {
                        try {
                            charts.systemChart = echarts.init(systemElement, null, {
                                renderer: 'canvas',
                                useDirtyRect: false,
                                width: systemElement.offsetWidth || 300,
                                height: systemElement.offsetHeight || 250
                            });
                            console.log('systemChart 初始化完成');
                        } catch (e) {
                            console.error('systemChart 初始化失败:', e);
                        }
                    }
                    
                    // 站点巡检统计柱状图
                    const stationElement = document.getElementById('stationChart');
                    if (stationElement && !charts.stationChart) {
                        try {
                            charts.stationChart = echarts.init(stationElement, null, {
                                renderer: 'canvas',
                                useDirtyRect: false,
                                width: stationElement.offsetWidth || 300,
                                height: stationElement.offsetHeight || 250
                            });
                            console.log('stationChart 初始化完成');
                        } catch (e) {
                            console.error('stationChart 初始化失败:', e);
                        }
                    }
                    
                    // 站点异常统计柱状图
                    const issueElement = document.getElementById('issueChart');
                    if (issueElement && !charts.issueChart) {
                        try {
                            charts.issueChart = echarts.init(issueElement, null, {
                                renderer: 'canvas',
                                useDirtyRect: false,
                                width: issueElement.offsetWidth || 300,
                                height: issueElement.offsetHeight || 250
                            });
                            console.log('issueChart 初始化完成');
                        } catch (e) {
                            console.error('issueChart 初始化失败:', e);
                        }
                    }
                    
                    // 趋势分析折线图
                    const trendElement = document.getElementById('trendChart');
                    if (trendElement && !charts.trendChart) {
                        try {
                            charts.trendChart = echarts.init(trendElement, null, {
                                renderer: 'canvas',
                                useDirtyRect: false,
                                width: trendElement.offsetWidth || 300,
                                height: trendElement.offsetHeight || 400
                            });
                            console.log('trendChart 初始化完成');
                        } catch (e) {
                            console.error('trendChart 初始化失败:', e);
                        }
                    }
                    
                    console.log('所有图表初始化完成');
                    
                    // 添加窗口大小变化监听
                    window.addEventListener('resize', () => {
                        Object.values(charts).forEach(chart => {
                            if (chart) {
                                chart.resize();
                            }
                        });
                    });
                    
                    // 初始化完成后加载数据
                    loadDashboardData();
                    loadTrendsData();
                });
            }, 100);
        };

        // 加载线路选项
        const loadLineOptions = async () => {
            try {
                const response = await fetch('/api/equipment_inspection/lines', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    lineOptions.value = data.map(line => ({
                        label: line.name,
                        value: line.id
                    }));
                    console.log('线路选项加载成功:', lineOptions.value);
                } else {
                    console.error('加载线路选项失败:', response.status);
                }
            } catch (error) {
                console.error('加载线路选项出错:', error);
            }
        };

        // 加载专业选项
        const loadMajorOptions = async () => {
            try {
                const response = await fetch('/api/equipment_inspection/majors', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    majorOptions.value = data.map(major => ({
                        label: major.name,
                        value: major.id
                    }));
                    console.log('专业选项加载成功:', majorOptions.value);
                } else {
                    console.error('加载专业选项失败:', response.status);
                }
            } catch (error) {
                console.error('加载专业选项出错:', error);
            }
        };

        // 加载看板数据
        const loadDashboardData = async () => {
            try {
                loading.value = true;
                
                const requestData = {};
                if (selectedMonth.value) {
                    requestData.month = selectedMonth.value;
                }
                if (selectedLine.value) {
                    requestData.line_id = selectedLine.value;
                }
                if (selectedMajor.value) {
                    requestData.major_id = selectedMajor.value;
                }
                
                const response = await fetch('/api/equipment_inspection/dashboard_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        dashboardData.value = data;
                        updateCharts(data);
                        updateDataScope();
                        console.log('看板数据加载成功:', data);
                    } else {
                        console.error('看板数据加载失败:', data.error);
                    }
                } else {
                    console.error('看板数据请求失败:', response.status);
                }
            } catch (error) {
                console.error('加载看板数据出错:', error);
            } finally {
                loading.value = false;
            }
        };

        // 更新数据范围显示
        const updateDataScope = () => {
            const scopes = [];
            if (selectedMonth.value) {
                scopes.push(`${selectedMonth.value}月`);
            }
            if (selectedLine.value) {
                const line = lineOptions.value.find(l => l.value === selectedLine.value);
                if (line) scopes.push(line.label);
            }
            if (selectedMajor.value) {
                const major = majorOptions.value.find(m => m.value === selectedMajor.value);
                if (major) scopes.push(major.label);
            }
            currentDataScope.value = scopes.length > 0 ? scopes.join(' | ') : null;
        };

        return {
            currentDate,
            currentTime,
            selectedMonth,
            selectedLine,
            selectedMajor,
            lineOptions,
            majorOptions,
            currentDataScope,
            loading,
            showDrillBackBtn,
            trendViewMode,
            dashboardData,
            updateDateTime,
            initCharts,
            loadLineOptions,
            loadMajorOptions,
            loadDashboardData,
            // 事件处理方法将在下一部分添加
            onMonthChange: () => loadDashboardData(),
            onLineChange: () => loadDashboardData(),
            onMajorChange: () => loadDashboardData(),
            onTrendViewModeChange: () => loadTrendsData(),
            handleDrillBack: () => {
                showDrillBackBtn.value = false;
                loadDashboardData();
            },
            enterSystem: () => {
                window.location.href = '/web';
            }
        };
    }
});

// 启动应用
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM加载完成，开始启动Vue应用');
    
    // 移除加载状态
    document.body.removeAttribute('data-loading');
    
    // 挂载Vue应用
    vueInstance = dashboardApp.use(ElementPlus, {
        locale: ElementPlusLocaleZhCn
    });
    
    // 注册Element Plus图标
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
        vueInstance.component(key, component);
    }
    
    const app = vueInstance.mount('#app');
    
    console.log('Vue应用启动完成');
    
    // 启动时间更新
    app.updateDateTime();
    app.timeInterval = setInterval(app.updateDateTime, 1000);
    
    // 加载选项数据
    app.loadLineOptions();
    app.loadMajorOptions();
    
    // 初始化图表
    app.initCharts();
});

// 更新图表数据
function updateCharts(data) {
    console.log('开始更新图表数据:', data);

    // 更新专业分布饼图
    if (charts.majorChart && data.major_stats) {
        const majorOption = {
            title: {
                text: '专业分布',
                left: 'center',
                top: 20,
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: '#2c3e50'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 'left',
                top: 'middle',
                textStyle: {
                    fontSize: 12
                }
            },
            series: [{
                name: '专业分布',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['60%', '50%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '18',
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: data.major_stats.map((item, index) => ({
                    value: item.value,
                    name: item.name,
                    itemStyle: {
                        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'][index % 9]
                    }
                }))
            }]
        };
        charts.majorChart.setOption(majorOption);
    }

    // 更新系统分布柱状图
    if (charts.systemChart && data.system_stats) {
        const systemOption = {
            title: {
                text: '系统分布',
                left: 'center',
                top: 20,
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: '#2c3e50'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: data.system_stats.map(item => item.name),
                axisLabel: {
                    rotate: 45,
                    fontSize: 10
                }
            },
            yAxis: {
                type: 'value'
            },
            series: [{
                name: '巡检次数',
                type: 'bar',
                data: data.system_stats.map(item => item.value),
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#83bff6' },
                        { offset: 0.5, color: '#188df0' },
                        { offset: 1, color: '#188df0' }
                    ])
                },
                emphasis: {
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#2378f7' },
                            { offset: 0.7, color: '#2378f7' },
                            { offset: 1, color: '#83bff6' }
                        ])
                    }
                }
            }]
        };
        charts.systemChart.setOption(systemOption);
    }

    // 更新站点巡检统计柱状图
    if (charts.stationChart && data.station_stats) {
        const stationOption = {
            title: {
                text: '站点巡检统计',
                left: 'center',
                top: 20,
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: '#2c3e50'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: data.station_stats.map(item => item.name),
                axisLabel: {
                    rotate: 45,
                    fontSize: 10
                }
            },
            yAxis: {
                type: 'value'
            },
            series: [{
                name: '巡检次数',
                type: 'bar',
                data: data.station_stats.map(item => item.value),
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#ffd666' },
                        { offset: 0.5, color: '#f7ba2a' },
                        { offset: 1, color: '#f7ba2a' }
                    ])
                }
            }]
        };
        charts.stationChart.setOption(stationOption);
    }

    // 更新站点异常统计柱状图
    if (charts.issueChart && data.station_issue_stats) {
        const issueOption = {
            title: {
                text: '站点异常统计',
                left: 'center',
                top: 20,
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: '#2c3e50'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: data.station_issue_stats.map(item => item.name),
                axisLabel: {
                    rotate: 45,
                    fontSize: 10
                }
            },
            yAxis: {
                type: 'value'
            },
            series: [{
                name: '异常次数',
                type: 'bar',
                data: data.station_issue_stats.map(item => item.value),
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#ff7875' },
                        { offset: 0.5, color: '#ff4d4f' },
                        { offset: 1, color: '#ff4d4f' }
                    ])
                }
            }]
        };
        charts.issueChart.setOption(issueOption);
    }
}

// 加载趋势数据
async function loadTrendsData() {
    try {
        const vueApp = vueInstance._instance.proxy;

        const requestData = {
            days: 30,
            view_mode: vueApp.trendViewMode
        };

        if (vueApp.selectedMonth) {
            requestData.month = vueApp.selectedMonth;
        }
        if (vueApp.selectedLine) {
            requestData.line_id = vueApp.selectedLine;
        }
        if (vueApp.selectedMajor) {
            requestData.major_id = vueApp.selectedMajor;
        }

        const response = await fetch('/api/equipment_inspection/trends', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                updateTrendChart(data.trends);
                console.log('趋势数据加载成功:', data);
            } else {
                console.error('趋势数据加载失败:', data.error);
            }
        } else {
            console.error('趋势数据请求失败:', response.status);
        }
    } catch (error) {
        console.error('加载趋势数据出错:', error);
    }
}

// 更新趋势图表
function updateTrendChart(trendsData) {
    if (!charts.trendChart || !trendsData) return;

    const trendOption = {
        title: {
            text: '巡检趋势分析',
            left: 'center',
            top: 20,
            textStyle: {
                fontSize: 16,
                fontWeight: 'bold',
                color: '#2c3e50'
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        legend: {
            data: ['总数', '已完成', '异常'],
            top: 50
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '20%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: trendsData.dates || []
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '总数',
                type: 'line',
                stack: 'Total',
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(84, 112, 198, 0.3)' },
                        { offset: 1, color: 'rgba(84, 112, 198, 0.1)' }
                    ])
                },
                emphasis: {
                    focus: 'series'
                },
                data: trendsData.total || []
            },
            {
                name: '已完成',
                type: 'line',
                stack: 'Total',
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(145, 204, 117, 0.3)' },
                        { offset: 1, color: 'rgba(145, 204, 117, 0.1)' }
                    ])
                },
                emphasis: {
                    focus: 'series'
                },
                data: trendsData.completed || []
            },
            {
                name: '异常',
                type: 'line',
                stack: 'Total',
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(238, 102, 102, 0.3)' },
                        { offset: 1, color: 'rgba(238, 102, 102, 0.1)' }
                    ])
                },
                emphasis: {
                    focus: 'series'
                },
                data: trendsData.abnormal || []
            }
        ]
    };

    charts.trendChart.setOption(trendOption);
}
