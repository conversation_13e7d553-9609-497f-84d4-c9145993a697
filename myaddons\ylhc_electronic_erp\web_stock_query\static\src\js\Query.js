import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";
import { Component, useState, onWillStart, useRef, onMounted } from "@odoo/owl";

export class StockQuery extends Component {
    static props = {};
    setup() {
        this.orm = useService('orm');
        this.action = useService("action");
        this.inputRef = useRef("searchInput");
        this.searchListRef = useRef("searchList");
        this.state = useState({
            text: "",
            countValue: 0,
            goods: [],
            isVisible: false
        });
        onWillStart(this.onWillStart);
        onMounted(() => {
            // 组件挂载后，如果输入框有焦点，确保光标在末尾
            if (this.inputRef.el && this.inputRef.el === document.activeElement) {
                this.setCursorToEnd();
            }
        });
    }

    setCursorToEnd() {
        if (this.inputRef.el) {
            const input = this.inputRef.el;
            const length = input.value.length;
            input.setSelectionRange(length, length);
        }
    }

    onFocus() {
        // 当输入框获得焦点时，将光标移到末尾
        setTimeout(() => {
            this.setCursorToEnd();
        }, 0);
        // 如果有内容就触发搜索，没有内容就准备接受输入
        if (this.state.text.length > 0) {
            this.onWillStart();
        }
    }

    // 新增：外部调用的聚焦方法，用于快捷键触发
    focusSearch() {
        if (this.inputRef.el) {
            this.inputRef.el.focus();
            this.setCursorToEnd();
        }
    }

    // 清空搜索框
    onClearSearch() {
        this.state.text = "";
        this.state.isVisible = false;
        this.state.countValue = 0;
        this.state.goods = [];
        // 清空后重新聚焦到输入框，保持展开状态
        setTimeout(() => {
            if (this.inputRef.el) {
                this.inputRef.el.focus();
            }
        }, 0);
    }

    onBlur() {
        // 当输入框失去焦点时，延迟关闭候选框
        // 使用延迟是为了允许用户点击候选项
        setTimeout(() => {
            this.state.isVisible = false;
            setTimeout(() => {
                this.state.countValue = 0;
            }, 250); // 等待动画完成后再移除DOM元素
        }, 200);
    }

    async onWillStart() {
        if (this.state.text.length > 0){
            // 首先搜索产品别名
            let aliasData = await this.orm.searchRead("product.alias", [
                ['name', 'ilike', this.state.text.trim()]
            ], ["product_id"], { limit: 80 });
            
            // 提取别名对应的产品ID
            let aliasProductIds = aliasData.map(alias => alias.product_id[0]);
            
            // 构建搜索条件：包含原有的搜索条件和别名产品ID
            let domain = ['|', '|', '|',
                ['default_code', 'ilike', this.state.text.trim()],
                ['name', 'ilike', this.state.text.trim()],
                ['description', 'ilike', this.state.text.trim()],
                ['id', 'in', aliasProductIds]
            ];
            
            let data = await this.orm.searchRead("product.product", domain, 
                ["id", "default_code","display_name","qty_available","base_unit_name"],
                { limit: 80 });
            
            if (data.length > 0) {
                this.state.countValue = data.length;
                this.state.goods = data;
                // 先设置DOM元素，然后触发动画
                setTimeout(() => {
                    this.state.isVisible = true;
                }, 10);
            } else {
                // 搜索有内容但没有结果，显示"未找到相关产品"提示
                this.state.countValue = 0;
                this.state.goods = [];
                setTimeout(() => {
                    this.state.isVisible = true;
                }, 10);
            }
        } else {
            // 输入为空时，清空数据并隐藏候选框
            this.state.countValue = 0;
            this.state.goods = [];
            this.state.isVisible = false;
        }
    }

    async onClose() {
        this.state.isVisible = false;
        setTimeout(() => {
            this.state.countValue = 0;
        }, 250);
    }

    async onSingleClick(id, default_code) {
        this.state.text = "";
        const viewID = await this.orm.searchRead("ir.ui.view",[["name", "=", "product.product.stock.list"]],["id"]);
        this.action.doAction({
            type: 'ir.actions.act_window',
            name: '搜索: 单个商品',
            res_model: 'product.product',
            views: [[viewID[0].id, 'list'], [false, 'form']],
            view_mode: 'list',
            target: 'current',
            domain: [['id', '=', id]]
        });
    }

    async onTotalClick() {
        const viewID = await this.orm.searchRead("ir.ui.view",[["name", "=", "product.product.stock.list"]],["id"]);
        this.action.doAction({
            type: 'ir.actions.act_window',
            name: '搜索: 全部商品',
            res_model: 'product.product',
            views: [[viewID[0].id, 'list'],[false, 'form']],
            view_mode: 'list',
            target: 'current',
        });
    }

    async onQuickCreate() {
        // 快速创建产品，将搜索文本作为产品名称传递
        this.action.doAction({
            type: 'ir.actions.act_window',
            name: '快速创建产品',
            res_model: 'product.quickly.create.wizard',
            views: [[false, 'form']],
            view_mode: 'form',
            target: 'new',
            context: { 
                'default_product_name': this.state.text.trim()
            }
        });
        // 清空搜索框并关闭候选框
        this.state.text = "";
        this.state.isVisible = false;
        this.state.countValue = 0;
    }
}

StockQuery.template = 'web_stock_query.stock_query'

export const systrayItem = {

    Component: StockQuery,
};
registry.category("systray").add("StockQuery", systrayItem);
